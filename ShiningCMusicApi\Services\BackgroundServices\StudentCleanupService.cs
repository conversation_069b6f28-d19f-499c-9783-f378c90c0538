using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class StudentCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<StudentCleanupService> _logger;
        private readonly IConfiguration _configuration;
        private readonly TimeSpan _period;
        private readonly int _retentionDays;

        public StudentCleanupService(
            IServiceProvider serviceProvider,
            ILogger<StudentCleanupService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;

            // Read configuration settings - check environment variables first, then fall back to appsettings.json
            var cleanupIntervalHours = GetConfigurationValue("STUDENT_CLEANUP_INTERVAL_HOURS", "StudentCleanup:IntervalHours", 24);
            _retentionDays = GetConfigurationValue("STUDENT_CLEANUP_RETENTION_DAYS", "StudentCleanup:RetentionDays", 30);
            _period = TimeSpan.FromHours(cleanupIntervalHours);

            _logger.LogInformation("StudentCleanupService initialized. Will run every {Hours} hours and delete archived students older than {Days} days.",
                cleanupIntervalHours, _retentionDays);
        }

        private int GetConfigurationValue(string environmentVariableName, string configurationKey, int defaultValue)
        {
            // First try environment variable
            var envValue = Environment.GetEnvironmentVariable(environmentVariableName);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", environmentVariableName, envIntValue);
                return envIntValue;
            }

            // Fall back to configuration
            var configValue = _configuration.GetValue<int>(configurationKey, defaultValue);
            _logger.LogInformation("Using configuration {ConfigKey} = {Value} (default: {Default})",
                configurationKey, configValue, defaultValue);
            return configValue;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("StudentCleanupService started.");

            // Wait for initial delay to avoid running immediately on startup
            await Task.Delay(TimeSpan.FromMinutes(3), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformCleanupAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during student cleanup.");
                }

                try
                {
                    await Task.Delay(_period, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("StudentCleanupService stopped.");
        }

        private async Task PerformCleanupAsync()
        {
            _logger.LogInformation("Starting student cleanup process...");

            using var scope = _serviceProvider.CreateScope();
            var studentService = scope.ServiceProvider.GetRequiredService<IStudentService>();

            try
            {
                var deletedCount = await studentService.PermanentlyDeleteArchivedStudentsAsync(_retentionDays);

                if (deletedCount > 0)
                {
                    _logger.LogInformation("Successfully deleted {Count} archived students older than {Days} days.",
                        deletedCount, _retentionDays);
                }
                else
                {
                    _logger.LogInformation("No archived students older than {Days} days found for cleanup.", _retentionDays);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform student cleanup.");
                throw;
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("StudentCleanupService is stopping.");
            await base.StopAsync(stoppingToken);
        }
    }
}
