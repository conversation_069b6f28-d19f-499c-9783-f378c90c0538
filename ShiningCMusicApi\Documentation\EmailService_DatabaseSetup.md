# Email Service Database Setup Guide

## Overview

This guide provides step-by-step instructions for setting up the email template database tables and initial data for the Shining C Music Studio email system.

## Prerequisites

- SQL Server database access
- Database connection configured in application
- Appropriate permissions to create tables and insert data

## Database Schema

### Tables Created

1. **EmailTemplates** - Stores email template definitions
2. **EmailAttachments** - Stores file attachment references for templates

### Relationships

- EmailAttachments.TemplateName → EmailTemplates.Name (Foreign Key with CASCADE DELETE)

## Setup Instructions

### Step 1: Run Database Script

Execute the provided SQL script to create tables and initial data:

```sql
-- Location: ShiningCMusicAPI/SQL/Add_Email_Templates.sql
-- Execute this script against your MusicSchool database
```

### Step 2: Verify Table Creation

Check that tables were created successfully:

```sql
-- Verify EmailTemplates table
SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'EmailTemplates'
ORDER BY ORDINAL_POSITION;

-- Verify EmailAttachments table
SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'EmailAttachments'
ORDER BY ORDINAL_POSITION;
```

### Step 3: Verify Initial Data

Check that the ScheduleReady template was created:

```sql
-- Check initial template
SELECT Name, Subject, 
       CASE WHEN BodyHtml IS NOT NULL THEN 'HTML Available' ELSE 'No HTML' END as HTMLStatus,
       CASE WHEN BodyText IS NOT NULL THEN 'Text Available' ELSE 'No Text' END as TextStatus
FROM EmailTemplates;
```

## Table Specifications

### EmailTemplates Table

| Column | Type | Length | Nullable | Description |
|--------|------|--------|----------|-------------|
| Name | nvarchar | 50 | No | Primary key, template identifier |
| CcEmailAddresses | nvarchar | 256 | Yes | Semicolon-separated CC recipients |
| BccEmailAddresses | nvarchar | 256 | Yes | Semicolon-separated BCC recipients |
| Subject | nvarchar | 500 | Yes | Email subject with placeholder support |
| BodyText | nvarchar | max | Yes | Plain text version of email |
| BodyHtml | nvarchar | max | Yes | HTML version of email |

### EmailAttachments Table

| Column | Type | Length | Nullable | Description |
|--------|------|--------|----------|-------------|
| ID | int | - | No | Primary key, auto-increment |
| TemplateName | nvarchar | 50 | No | Foreign key to EmailTemplates.Name |
| AttachmentName | nvarchar | 256 | No | Display name for attachment |
| AttachmentPath | nvarchar | 512 | No | File system path to attachment |

## Initial Template: ScheduleReady

### Template Details

- **Name:** ScheduleReady
- **Subject:** Your Schedule is Ready - Shining C Music Studio
- **Purpose:** Notify tutors when their teaching schedule is updated
- **Placeholders:** {TutorName}

### HTML Version Features

- Professional Shining C Music Studio branding
- Responsive design for mobile and desktop
- Blue color scheme (#007bff)
- Call-to-action link to login portal
- Professional footer with disclaimer

### Plain Text Version

- Clean, readable format
- All essential information included
- Fallback for email clients that don't support HTML

## Adding Custom Templates

### Example: Lesson Reminder Template

```sql
INSERT INTO EmailTemplates (Name, Subject, BodyHtml, BodyText)
VALUES (
    'LessonReminder',
    'Lesson Reminder - {LessonDate}',
    N'<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
        .content { background-color: #f8f9fa; padding: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Lesson Reminder</h1>
        </div>
        <div class="content">
            <h2>Hello {StudentName}!</h2>
            <p>This is a friendly reminder about your upcoming music lesson:</p>
            <ul>
                <li><strong>Date:</strong> {LessonDate}</li>
                <li><strong>Time:</strong> {LessonTime}</li>
                <li><strong>Instructor:</strong> {InstructorName}</li>
                <li><strong>Location:</strong> {Location}</li>
            </ul>
            <p>Please arrive 5 minutes early and bring your music books.</p>
            <p>If you need to reschedule, please contact us at least 24 hours in advance.</p>
        </div>
    </div>
</body>
</html>',
    N'Hello {StudentName}!

This is a friendly reminder about your upcoming music lesson:

Date: {LessonDate}
Time: {LessonTime}
Instructor: {InstructorName}
Location: {Location}

Please arrive 5 minutes early and bring your music books.

If you need to reschedule, please contact us at least 24 hours in advance.

Best regards,
Shining C Music Studio'
);
```

### Example: Welcome Email Template

```sql
INSERT INTO EmailTemplates (Name, Subject, BodyHtml, BodyText)
VALUES (
    'StudentWelcome',
    'Welcome to Shining C Music Studio, {StudentName}!',
    N'<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
        .content { background-color: #f8f9fa; padding: 30px; }
        .highlight { background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Welcome to Shining C Music Studio!</h1>
        </div>
        <div class="content">
            <h2>Hello {StudentName}!</h2>
            <p>We''re excited to welcome you to our music family!</p>
            
            <div class="highlight">
                <h3>Your First Lesson Details:</h3>
                <p><strong>Date:</strong> {StartDate}<br>
                <strong>Instructor:</strong> {InstructorName}<br>
                <strong>Instrument:</strong> {Instrument}</p>
            </div>
            
            <p>What to bring to your first lesson:</p>
            <ul>
                <li>A positive attitude and eagerness to learn</li>
                <li>A notebook for taking notes</li>
                <li>Any music books you may already have</li>
            </ul>
            
            <p>We look forward to helping you on your musical journey!</p>
        </div>
    </div>
</body>
</html>',
    N'Welcome to Shining C Music Studio!

Hello {StudentName}!

We''re excited to welcome you to our music family!

Your First Lesson Details:
Date: {StartDate}
Instructor: {InstructorName}
Instrument: {Instrument}

What to bring to your first lesson:
- A positive attitude and eagerness to learn
- A notebook for taking notes
- Any music books you may already have

We look forward to helping you on your musical journey!

Best regards,
Shining C Music Studio Team'
);
```

## Adding Attachments

### Example: Add Schedule PDF Attachment

```sql
-- First, ensure the template exists
INSERT INTO EmailAttachments (TemplateName, AttachmentName, AttachmentPath)
VALUES (
    'ScheduleReady',
    'Monthly_Schedule.pdf',
    '/files/schedules/monthly_schedule.pdf'
);
```

### Example: Add Welcome Package Attachments

```sql
-- Multiple attachments for welcome email
INSERT INTO EmailAttachments (TemplateName, AttachmentName, AttachmentPath)
VALUES 
    ('StudentWelcome', 'Student_Handbook.pdf', '/files/handbooks/student_handbook.pdf'),
    ('StudentWelcome', 'Practice_Schedule.pdf', '/files/schedules/practice_schedule_template.pdf'),
    ('StudentWelcome', 'Studio_Policies.pdf', '/files/policies/studio_policies.pdf');
```

## Maintenance Queries

### View All Templates

```sql
SELECT 
    t.Name,
    t.Subject,
    CASE WHEN t.BodyHtml IS NOT NULL THEN 'Yes' ELSE 'No' END as HasHTML,
    CASE WHEN t.BodyText IS NOT NULL THEN 'Yes' ELSE 'No' END as HasText,
    COUNT(a.ID) as AttachmentCount
FROM EmailTemplates t
LEFT JOIN EmailAttachments a ON t.Name = a.TemplateName
GROUP BY t.Name, t.Subject, t.BodyHtml, t.BodyText
ORDER BY t.Name;
```

### View Template with Attachments

```sql
SELECT 
    t.Name as TemplateName,
    t.Subject,
    a.AttachmentName,
    a.AttachmentPath
FROM EmailTemplates t
LEFT JOIN EmailAttachments a ON t.Name = a.TemplateName
WHERE t.Name = 'ScheduleReady'
ORDER BY a.AttachmentName;
```

### Update Template Content

```sql
-- Update subject and body
UPDATE EmailTemplates 
SET Subject = 'Updated Subject - {TutorName}',
    BodyHtml = N'<h1>Updated HTML content with {TutorName}</h1>'
WHERE Name = 'ScheduleReady';
```

### Delete Template and Attachments

```sql
-- This will automatically delete attachments due to CASCADE DELETE
DELETE FROM EmailTemplates WHERE Name = 'TemplateName';
```

## Backup and Recovery

### Backup Templates

```sql
-- Export templates to backup
SELECT * FROM EmailTemplates;
SELECT * FROM EmailAttachments;
```

### Restore from Backup

```sql
-- Restore templates (adjust values as needed)
INSERT INTO EmailTemplates (Name, Subject, BodyHtml, BodyText, CcEmailAddresses, BccEmailAddresses)
VALUES (...);

INSERT INTO EmailAttachments (TemplateName, AttachmentName, AttachmentPath)
VALUES (...);
```

## Troubleshooting

### Common Issues

1. **Foreign Key Constraint Errors**
   - Ensure template exists before adding attachments
   - Check template name spelling (case-sensitive)

2. **Template Not Found in Application**
   - Verify template name matches exactly
   - Check database connection string
   - Ensure application has read permissions

3. **Attachment Files Not Found**
   - Verify file paths are correct
   - Ensure application has read access to file directories
   - Check file permissions

### Validation Queries

```sql
-- Check for orphaned attachments
SELECT a.* 
FROM EmailAttachments a
LEFT JOIN EmailTemplates t ON a.TemplateName = t.Name
WHERE t.Name IS NULL;

-- Check for templates without content
SELECT Name, Subject
FROM EmailTemplates 
WHERE (BodyHtml IS NULL OR BodyHtml = '') 
  AND (BodyText IS NULL OR BodyText = '');

-- Check for missing placeholders in subject/body
SELECT Name, Subject, 
       CASE WHEN Subject LIKE '%{%}%' THEN 'Has Placeholders' ELSE 'No Placeholders' END as SubjectPlaceholders
FROM EmailTemplates;
```

## Performance Considerations

- **Indexing:** Primary keys are automatically indexed
- **Template Caching:** Consider application-level caching for frequently used templates
- **Large Attachments:** Monitor attachment file sizes to prevent memory issues
- **Bulk Operations:** Use transactions for multiple template operations

## Security Notes

- **File Paths:** Validate attachment paths to prevent directory traversal attacks
- **Content Validation:** Sanitize user-provided template content
- **Access Control:** Ensure only authorized users can modify templates
- **Backup:** Regularly backup template data
