using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EmailTemplatesController : ControllerBase
    {
        private readonly IEmailTemplateService _emailTemplateService;
        private readonly ILogger<EmailTemplatesController> _logger;

        public EmailTemplatesController(
            IEmailTemplateService emailTemplateService,
            ILogger<EmailTemplatesController> logger)
        {
            _emailTemplateService = emailTemplateService;
            _logger = logger;
        }

        // GET: api/emailtemplates
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmailTemplate>>> GetTemplates()
        {
            try
            {
                var templates = await _emailTemplateService.GetAllTemplatesAsync();
                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email templates");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: api/emailtemplates/{templateName}
        [HttpGet("{templateName}")]
        public async Task<ActionResult<EmailTemplate>> GetTemplate(string templateName)
        {
            try
            {
                var template = await _emailTemplateService.GetTemplateAsync(templateName);
                if (template == null)
                {
                    return NotFound(new { message = "Email template not found" });
                }
                return Ok(template);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email template");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: api/emailtemplates
        [HttpPost]
        public async Task<ActionResult<EmailTemplate>> CreateTemplate([FromBody] EmailTemplate template)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(template.Name))
                {
                    return BadRequest(new { message = "Template name is required" });
                }

                var createdTemplate = await _emailTemplateService.CreateTemplateAsync(template);
                return CreatedAtAction(nameof(GetTemplate), new { templateName = createdTemplate.Name }, createdTemplate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating email template");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: api/emailtemplates/{templateName}
        [HttpPut("{templateName}")]
        public async Task<IActionResult> UpdateTemplate(string templateName, [FromBody] EmailTemplate template)
        {
            try
            {
                var success = await _emailTemplateService.UpdateTemplateAsync(templateName, template);
                if (success)
                {
                    return Ok(new { message = "Email template updated successfully" });
                }
                return NotFound(new { message = "Email template not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email template");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: api/emailtemplates/{templateName}
        [HttpDelete("{templateName}")]
        public async Task<IActionResult> DeleteTemplate(string templateName)
        {
            try
            {
                var success = await _emailTemplateService.DeleteTemplateAsync(templateName);
                if (success)
                {
                    return Ok(new { message = "Email template deleted successfully" });
                }
                return NotFound(new { message = "Email template not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting email template");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: api/emailtemplates/{templateName}/attachments
        [HttpGet("{templateName}/attachments")]
        public async Task<ActionResult<IEnumerable<EmailAttachment>>> GetTemplateAttachments(string templateName)
        {
            try
            {
                var attachments = await _emailTemplateService.GetTemplateAttachmentsAsync(templateName);
                return Ok(attachments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving template attachments");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: api/emailtemplates/attachments
        [HttpPost("attachments")]
        public async Task<ActionResult<EmailAttachment>> AddAttachment([FromBody] EmailAttachment attachment)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(attachment.TemplateName) || 
                    string.IsNullOrWhiteSpace(attachment.AttachmentName) || 
                    string.IsNullOrWhiteSpace(attachment.AttachmentPath))
                {
                    return BadRequest(new { message = "Template name, attachment name, and attachment path are required" });
                }

                var createdAttachment = await _emailTemplateService.AddAttachmentAsync(attachment);
                return Ok(createdAttachment);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding attachment");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: api/emailtemplates/attachments/{attachmentId}
        [HttpDelete("attachments/{attachmentId}")]
        public async Task<IActionResult> DeleteAttachment(int attachmentId)
        {
            try
            {
                var success = await _emailTemplateService.DeleteAttachmentAsync(attachmentId);
                if (success)
                {
                    return Ok(new { message = "Attachment deleted successfully" });
                }
                return NotFound(new { message = "Attachment not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attachment");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
