using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp.Services;
using ShiningCMusicCommon.Enums;
using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicApp.Pages;

public partial class LoginBase : ComponentBase
{
    [Inject] protected CustomAuthenticationStateProvider AuthStateProvider { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;

    protected LoginModel loginModel = new();
    protected bool isLoggingIn = false;
    protected string errorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            // Redirect to home page if already logged in
            Navigation.NavigateTo("/");
        }
    }

    protected async Task HandleLogin()
    {
        isLoggingIn = true;
        errorMessage = string.Empty;

        try
        {
            var success = await AuthStateProvider.LoginAsync(loginModel.LoginName, loginModel.Password);

            if (success)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "Login successful");

                // Get the current user to determine role
                var user = await AuthStateProvider.GetCurrentUserAsync();

                // Navigate based on user role
                if (user?.RoleId == (int)UserRoleEnum.Administrator)
                {
                    Navigation.NavigateTo("/", true);
                }
                else if (user?.RoleId == (int)UserRoleEnum.Tutor || user?.RoleId == (int)UserRoleEnum.Student)
                {
                    Navigation.NavigateTo("/lessons", true);
                }
                else
                {
                    Navigation.NavigateTo("/", true);
                }
            }
            else
            {
                errorMessage = "Invalid login name or password. Please try again.";
                await JSRuntime.InvokeVoidAsync("console.error", $"Login failed: {errorMessage}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            await JSRuntime.InvokeVoidAsync("console.error", $"Login error: {ex.Message}");
        }
        finally
        {
            isLoggingIn = false;
        }
    }

    protected async Task HandleKeyPress(Microsoft.AspNetCore.Components.Web.KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !isLoggingIn)
        {
            await HandleLogin();
        }
    }

    public class LoginModel
    {
        [Required(ErrorMessage = "Login name is required")]
        public string LoginName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;
    }
}
