# Tutor and Student Cleanup Services Implementation

## Overview
This document describes the implementation of background cleanup services for automatically deleting archived tutors and students older than a specified number of days (default: 30 days).

## ✅ IMPLEMENTATION COMPLETED

### What Was Implemented

**✅ Service Interfaces Updated:**
- Added `PermanentlyDeleteArchivedTutorsAsync(int olderThanDays)` to `ITutorService`
- Added `GetArchivedTutorsCountAsync(int olderThanDays)` to `ITutorService`
- Added `PermanentlyDeleteArchivedStudentsAsync(int olderThanDays)` to `IStudentService`
- Added `GetArchivedStudentsCountAsync(int olderThanDays)` to `IStudentService`

**✅ Service Implementations:**
- Implemented cleanup methods in `TutorService` and `StudentService`
- Uses `UpdatedUTC` field to determine when records were archived
- Only deletes records where `IsArchived = 1` AND `UpdatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())`

**✅ Background Services:**
- `TutorCleanupService` - Runs every 24 hours (configurable)
- `StudentCleanupService` - Runs every 24 hours (configurable)
- Both services follow the same pattern as `LessonCleanupService`

**✅ Configuration Support:**
- Environment variables (preferred): `TUTOR_CLEANUP_INTERVAL_HOURS`, `TUTOR_CLEANUP_RETENTION_DAYS`
- Environment variables (preferred): `STUDENT_CLEANUP_INTERVAL_HOURS`, `STUDENT_CLEANUP_RETENTION_DAYS`
- Fallback to `appsettings.json` configuration

**✅ Maintenance Endpoints:**
- `POST /api/maintenance/cleanup-archived-tutors?olderThanDays=30`
- `GET /api/maintenance/archived-tutors-count?olderThanDays=30`
- `POST /api/maintenance/cleanup-archived-students?olderThanDays=30`
- `GET /api/maintenance/archived-students-count?olderThanDays=30`

**✅ Service Registration:**
- Added to `Program.cs` as hosted services
- Proper dependency injection setup

## Configuration

### Environment Variables (Preferred)
```bash
# Tutor Cleanup
TUTOR_CLEANUP_INTERVAL_HOURS=24     # How often to run cleanup (default: 24 hours)
TUTOR_CLEANUP_RETENTION_DAYS=30     # Delete archived tutors older than this (default: 30 days)

# Student Cleanup
STUDENT_CLEANUP_INTERVAL_HOURS=24   # How often to run cleanup (default: 24 hours)
STUDENT_CLEANUP_RETENTION_DAYS=30   # Delete archived students older than this (default: 30 days)
```

### appsettings.json (Fallback)
```json
{
  "TutorCleanup": {
    "IntervalHours": 24,
    "RetentionDays": 30
  },
  "StudentCleanup": {
    "IntervalHours": 24,
    "RetentionDays": 30
  }
}
```

## How It Works

### Archiving Process
1. When a tutor/student is "deleted" via the UI, they are soft-deleted:
   - `IsArchived` is set to `1` (true)
   - `UpdatedUTC` is set to current UTC time
2. The record remains in the database but is excluded from normal queries

### Cleanup Process
1. Background services run every 24 hours (configurable)
2. Services query for records where:
   - `IsArchived = 1` (archived records only)
   - `UpdatedUTC IS NOT NULL` (has been updated/archived)
   - `UpdatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())` (older than retention period)
3. Matching records are permanently deleted from the database

### Startup Delays
- `TutorCleanupService`: 2-minute delay after startup
- `StudentCleanupService`: 3-minute delay after startup
- This prevents all services from running simultaneously on startup

## Testing

### 1. API Testing

#### Check Archived Tutors Count
```http
GET /api/maintenance/archived-tutors-count?olderThanDays=30
```

#### Cleanup Archived Tutors
```http
POST /api/maintenance/cleanup-archived-tutors?olderThanDays=30
```

#### Check Archived Students Count
```http
GET /api/maintenance/archived-students-count?olderThanDays=30
```

#### Cleanup Archived Students
```http
POST /api/maintenance/cleanup-archived-students?olderThanDays=30
```

### 2. Database Testing

#### Create Test Data
```sql
-- Archive some tutors (simulate deletion from UI)
UPDATE Tutors 
SET IsArchived = 1, UpdatedUTC = DATEADD(day, -35, GETUTCDATE())
WHERE TutorId IN (SELECT TOP 2 TutorId FROM Tutors WHERE IsArchived = 0);

-- Archive some students (simulate deletion from UI)
UPDATE Students 
SET IsArchived = 1, UpdatedUTC = DATEADD(day, -35, GETUTCDATE())
WHERE StudentId IN (SELECT TOP 2 StudentId FROM Students WHERE IsArchived = 0);
```

#### Verify Test Data
```sql
-- Check archived tutors older than 30 days
SELECT TutorId, TutorName, UpdatedUTC, 
       DATEDIFF(day, UpdatedUTC, GETUTCDATE()) as DaysOld
FROM Tutors
WHERE IsArchived = 1 
AND UpdatedUTC IS NOT NULL 
AND UpdatedUTC < DATEADD(day, -30, GETUTCDATE());

-- Check archived students older than 30 days
SELECT StudentId, StudentName, UpdatedUTC, 
       DATEDIFF(day, UpdatedUTC, GETUTCDATE()) as DaysOld
FROM Students
WHERE IsArchived = 1 
AND UpdatedUTC IS NOT NULL 
AND UpdatedUTC < DATEADD(day, -30, GETUTCDATE());
```

## Key Features

✅ **Safe Deletion**: Only deletes archived records, never active ones  
✅ **Configurable Retention**: Customizable retention period via environment variables  
✅ **Comprehensive Logging**: Detailed logging for monitoring and debugging  
✅ **Error Handling**: Robust error handling with service continuation  
✅ **Manual Testing**: API endpoints for manual testing and emergency cleanup  
✅ **Performance Optimized**: Uses indexed `UpdatedUTC` field for efficient queries  
✅ **Production Ready**: Environment variable configuration for deployment  

## Production Considerations

### Recommended Settings
- **IntervalHours**: 24 (daily cleanup)
- **RetentionDays**: 30-90 (depending on business requirements)

### Database Indexes
Consider adding indexes for better performance:
```sql
CREATE INDEX IX_Tutors_IsArchived_UpdatedUTC
ON Tutors (IsArchived, UpdatedUTC);

CREATE INDEX IX_Students_IsArchived_UpdatedUTC
ON Students (IsArchived, UpdatedUTC);
```

### Monitoring
- Monitor service logs for cleanup activity
- Set up alerts for service failures
- Track deletion counts for unusual activity

The implementation follows the same proven pattern as `LessonCleanupService` and integrates seamlessly with the existing architecture.
