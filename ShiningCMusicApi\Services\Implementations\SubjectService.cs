using Dapper;
using System.Data.SqlClient;
using ShiningCMusicCommon.Models;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.Implementations
{
    public class SubjectService : ISubjectService
    {
        private readonly string _connectionString;

        public SubjectService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<IEnumerable<Subject>> GetSubjectsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT SubjectId, Subject as SubjectName FROM Subjects ORDER BY Subject";
            return await connection.QueryAsync<Subject>(sql);
        }

        public async Task<Subject?> GetSubjectAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT SubjectId, Subject as SubjectName FROM Subjects WHERE SubjectId = @Id";
            return await connection.QueryFirstOrDefaultAsync<Subject>(sql, new { Id = id });
        }

        public async Task<Subject> CreateSubjectAsync(Subject subject)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Subjects (Subject)
                VALUES (@SubjectName);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                subject.SubjectName
            });

            subject.SubjectId = newId;
            return subject;
        }

        public async Task<bool> UpdateSubjectAsync(int id, Subject subject)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Subjects
                SET Subject = @SubjectName
                WHERE SubjectId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                subject.SubjectName
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteSubjectAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                DELETE FROM Subjects
                WHERE SubjectId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }
    }
}
