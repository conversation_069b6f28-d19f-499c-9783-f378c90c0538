using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface ITutorService
    {
        Task<IEnumerable<Tutor>> GetTutorsAsync();
        Task<Tutor?> GetTutorAsync(int id);
        Task<Tutor> CreateTutorAsync(<PERSON><PERSON> tutor);
        Task<bool> UpdateTutorAsync(int id, Tu<PERSON> tutor);
        Task<bool> DeleteTutorAsync(int id);
        Task<bool> UpdateTutorColorAsync(int tutorId, string color);
        Task<int> PermanentlyDeleteArchivedTutorsAsync(int olderThanDays);
        Task<int> GetArchivedTutorsCountAsync(int olderThanDays);
    }
}
