namespace ShiningCMusicApi.Services.Interfaces
{
    public interface IEmailService
    {
        Task<bool> SendScheduleReadyEmailAsync(string tutorEmail, string tutorName);
        Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true);
        Task<bool> SendEmailFromTemplateAsync(string templateName, string toEmail, Dictionary<string, string>? placeholders = null);
    }
}
