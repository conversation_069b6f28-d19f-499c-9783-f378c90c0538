USE [MusicSchool]
GO

/****** Object:  Table [dbo].[Lessons]    Script Date: 22/06/2025 8:30:00 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Lessons](
	[LessonId] [int] IDENTITY(1,1) NOT NULL,
	[SubjectId] [int] NOT NULL,
	[Description] [nvarchar](500) NULL,
	[StartTime] [datetime] NOT NULL,
	[EndTime] [datetime] NOT NULL,
	[TutorId] [int] NOT NULL,
	[StudentId] [int] NOT NULL,
	[LocationId] [int] NULL,
	[IsRecurring] [bit] NOT NULL DEFAULT 0,
	[RecurrenceRule] [nvarchar](500) NULL,
	[RecurrenceException] [nvarchar](1000) NULL,
	[RecurrenceID] [int] NULL,
	[CreatedUTC] [datetime] NOT NULL DEFAULT GETUTCDATE(),
	[UpdatedUTC] [datetime] NULL,
	[IsArchived] [bit] NOT NULL DEFAULT 0,
	[IsTrial] [bit] NOT NULL DEFAULT 0,
	[IsCancelled] [bit] NOT NULL DEFAULT 0,
PRIMARY KEY CLUSTERED 
(
	[LessonId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

-- Create index for better performance on recurring event queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Lessons_RecurrenceID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Lessons_RecurrenceID] ON [dbo].[Lessons]
    (
        [RecurrenceID] ASC
    ) WHERE [RecurrenceID] IS NOT NULL;
    PRINT 'Created index IX_Lessons_RecurrenceID';
END
ELSE
BEGIN
    PRINT 'Index IX_Lessons_RecurrenceID already exists';
END

-- Create index for recurring events
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Lessons_IsRecurring')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Lessons_IsRecurring] ON [dbo].[Lessons]
    (
        [IsRecurring] ASC
    ) WHERE [IsRecurring] = 1;
    PRINT 'Created index IX_Lessons_IsRecurring';
END
ELSE
BEGIN
    PRINT 'Index IX_Lessons_IsRecurring already exists';
END

-- Add foreign key constraints
--ALTER TABLE [dbo].[Lessons]  WITH CHECK ADD  CONSTRAINT [FK_Lessons_TutorId] FOREIGN KEY([TutorId])
--REFERENCES [dbo].[Tutors] ([TutorId])
--GO

--ALTER TABLE [dbo].[Lessons] CHECK CONSTRAINT [FK_Lessons_TutorId]
--GO

--ALTER TABLE [dbo].[Lessons]  WITH CHECK ADD  CONSTRAINT [FK_Lessons_StudentId] FOREIGN KEY([StudentId])
--REFERENCES [dbo].[Students] ([StudentId])
--GO

--ALTER TABLE [dbo].[Lessons] CHECK CONSTRAINT [FK_Lessons_StudentId]
--GO

-- Insert some sample data
INSERT INTO [dbo].[Lessons] ([SubjectId], [StartTime], [EndTime], [TutorId], [StudentId], [LocationId])
VALUES 
(1, '2025-06-23 10:00:00', '2025-06-23 11:00:00', 1, 1, 1),
(1, '2025-06-23 14:00:00', '2025-06-23 15:00:00', 1, 2, 1),
(1, '2025-06-24 09:00:00', '2025-06-24 10:30:00', 2, 1, 2),
(2, '2025-06-24 16:00:00', '2025-06-24 17:00:00', 1, 3, 2),
(1, '2025-06-25 11:00:00', '2025-06-25 12:00:00', 2, 2, 1)
GO
