# Email Service Environment Variables Guide

## Overview

The EmailService has been updated to read configuration from environment variables first, then fall back to appsettings.json. This follows security best practices by keeping sensitive information out of configuration files.

## Environment Variables

### Required Variables

| Variable Name | Description | Example Value |
|---------------|-------------|---------------|
| `EMAIL_SMTP_SERVER` | SMTP server hostname | `smtp.gmail.com` |
| `EMAIL_SMTP_PORT` | SMTP server port | `587` |
| `EMAIL_SENDER_EMAIL` | Sender email address | `<EMAIL>` |
| `EMAIL_SENDER_PASSWORD` | Email account password/app password | `evmnvpqszeqovljl` |
| `EMAIL_SENDER_NAME` | Display name for sender | `Shining C Music Studio` |

## Setting Environment Variables

### Windows (Development)

#### Command Prompt
```cmd
set EMAIL_SMTP_SERVER=smtp.gmail.com
set EMAIL_SMTP_PORT=587
set EMAIL_SENDER_EMAIL=<EMAIL>
set EMAIL_SENDER_PASSWORD=evmnvpqszeqovljl
set EMAIL_SENDER_NAME=Shining C Music Studio
```

#### PowerShell
```powershell
$env:EMAIL_SMTP_SERVER="smtp.gmail.com"
$env:EMAIL_SMTP_PORT="587"
$env:EMAIL_SENDER_EMAIL="<EMAIL>"
$env:EMAIL_SENDER_PASSWORD="evmnvpqszeqovljl"
$env:EMAIL_SENDER_NAME="Shining C Music Studio"
```

#### System Environment Variables
1. Open System Properties → Advanced → Environment Variables
2. Add each variable in the "System variables" section
3. Restart the application/service

### Linux/macOS (Development)

#### Bash/Zsh
```bash
export EMAIL_SMTP_SERVER="smtp.gmail.com"
export EMAIL_SMTP_PORT="587"
export EMAIL_SENDER_EMAIL="<EMAIL>"
export EMAIL_SENDER_PASSWORD="evmnvpqszeqovljl"
export EMAIL_SENDER_NAME="Shining C Music Studio"
```

#### Add to ~/.bashrc or ~/.zshrc for persistence
```bash
echo 'export EMAIL_SMTP_SERVER="smtp.gmail.com"' >> ~/.bashrc
echo 'export EMAIL_SMTP_PORT="587"' >> ~/.bashrc
echo 'export EMAIL_SENDER_EMAIL="<EMAIL>"' >> ~/.bashrc
echo 'export EMAIL_SENDER_PASSWORD="evmnvpqszeqovljl"' >> ~/.bashrc
echo 'export EMAIL_SENDER_NAME="Shining C Music Studio"' >> ~/.bashrc
source ~/.bashrc
```

### Azure App Service

#### Azure Portal
1. Go to your App Service
2. Navigate to Configuration → Application settings
3. Add each environment variable:
   - Name: `EMAIL_SMTP_SERVER`, Value: `smtp.gmail.com`
   - Name: `EMAIL_SMTP_PORT`, Value: `587`
   - Name: `EMAIL_SENDER_EMAIL`, Value: `<EMAIL>`
   - Name: `EMAIL_SENDER_PASSWORD`, Value: `evmnvpqszeqovljl`
   - Name: `EMAIL_SENDER_NAME`, Value: `Shining C Music Studio`
4. Click "Save"

#### Azure CLI
```bash
az webapp config appsettings set --resource-group myResourceGroup --name myAppName --settings \
  EMAIL_SMTP_SERVER="smtp.gmail.com" \
  EMAIL_SMTP_PORT="587" \
  EMAIL_SENDER_EMAIL="<EMAIL>" \
  EMAIL_SENDER_PASSWORD="evmnvpqszeqovljl" \
  EMAIL_SENDER_NAME="Shining C Music Studio"
```

#### ARM Template
```json
{
  "type": "Microsoft.Web/sites/config",
  "apiVersion": "2021-02-01",
  "name": "[concat(parameters('siteName'), '/appsettings')]",
  "properties": {
    "EMAIL_SMTP_SERVER": "smtp.gmail.com",
    "EMAIL_SMTP_PORT": "587",
    "EMAIL_SENDER_EMAIL": "<EMAIL>",
    "EMAIL_SENDER_PASSWORD": "evmnvpqszeqovljl",
    "EMAIL_SENDER_NAME": "Shining C Music Studio"
  }
}
```

### Docker

#### Dockerfile
```dockerfile
ENV EMAIL_SMTP_SERVER=smtp.gmail.com
ENV EMAIL_SMTP_PORT=587
ENV EMAIL_SENDER_EMAIL=<EMAIL>
ENV EMAIL_SENDER_PASSWORD=evmnvpqszeqovljl
ENV EMAIL_SENDER_NAME="Shining C Music Studio"
```

#### Docker Compose
```yaml
version: '3.8'
services:
  api:
    image: shiningcmusic-api
    environment:
      - EMAIL_SMTP_SERVER=smtp.gmail.com
      - EMAIL_SMTP_PORT=587
      - EMAIL_SENDER_EMAIL=<EMAIL>
      - EMAIL_SENDER_PASSWORD=evmnvpqszeqovljl
      - EMAIL_SENDER_NAME=Shining C Music Studio
```

#### Docker Run
```bash
docker run -d \
  -e EMAIL_SMTP_SERVER="smtp.gmail.com" \
  -e EMAIL_SMTP_PORT="587" \
  -e EMAIL_SENDER_EMAIL="<EMAIL>" \
  -e EMAIL_SENDER_PASSWORD="evmnvpqszeqovljl" \
  -e EMAIL_SENDER_NAME="Shining C Music Studio" \
  shiningcmusic-api
```

### Kubernetes

#### ConfigMap (for non-sensitive data)
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: email-config
data:
  EMAIL_SMTP_SERVER: "smtp.gmail.com"
  EMAIL_SMTP_PORT: "587"
  EMAIL_SENDER_EMAIL: "<EMAIL>"
  EMAIL_SENDER_NAME: "Shining C Music Studio"
```

#### Secret (for sensitive data)
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: email-secret
type: Opaque
data:
  EMAIL_SENDER_PASSWORD: ZXZtbnZwcXN6ZXFvdmxqbA== # base64 encoded
```

#### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: shiningcmusic-api
spec:
  template:
    spec:
      containers:
      - name: api
        image: shiningcmusic-api
        envFrom:
        - configMapRef:
            name: email-config
        - secretRef:
            name: email-secret
```

## Configuration Priority

The EmailService reads configuration in this order:

1. **Environment Variables** (highest priority)
2. **appsettings.json** (fallback)

This means:
- If `EMAIL_SMTP_SERVER` environment variable exists, it will be used
- If not, `EmailSettings:SmtpServer` from appsettings.json will be used
- If neither exists, the service will fail with a configuration error

## Security Best Practices

### Production Deployment
1. **Always use environment variables** for sensitive data like passwords
2. **Never commit passwords** to source control in appsettings.json
3. **Use Azure Key Vault** or similar secret management for production
4. **Rotate passwords regularly** and update environment variables

### Development
1. **Use appsettings.Development.json** for local development (not committed)
2. **Use user secrets** for sensitive development data
3. **Document required environment variables** for team members

### Gmail App Passwords
1. **Enable 2-Factor Authentication** on the Gmail account
2. **Generate App Password** specifically for this application
3. **Use App Password** instead of regular account password
4. **Revoke and regenerate** if compromised

## Verification

### Check Current Configuration
You can verify which configuration source is being used by checking the application logs. The EmailService will log configuration errors if settings are missing.

### Test Email Functionality
```bash
# Test with curl (replace with your API endpoint)
curl -X POST "https://your-api.com/api/email/send-schedule-ready/1" \
  -H "Authorization: Bearer your-token"
```

### Environment Variable Verification
```bash
# Windows
echo %EMAIL_SMTP_SERVER%

# Linux/macOS
echo $EMAIL_SMTP_SERVER

# PowerShell
$env:EMAIL_SMTP_SERVER
```

## Troubleshooting

### Common Issues

1. **Environment variables not set**
   - Check if variables are properly set in your deployment environment
   - Verify variable names match exactly (case-sensitive on Linux)

2. **Configuration not loading**
   - Restart the application after setting environment variables
   - Check application logs for configuration errors

3. **SMTP authentication failures**
   - Verify Gmail app password is correct
   - Ensure 2FA is enabled on Gmail account
   - Check if account is locked or requires additional verification

### Debug Configuration Loading
Add logging to verify which configuration source is being used:

```csharp
_logger.LogInformation("SMTP Server from env: {EnvServer}, from config: {ConfigServer}", 
    Environment.GetEnvironmentVariable("EMAIL_SMTP_SERVER"),
    _configuration["EmailSettings:SmtpServer"]);
```

## Migration from appsettings.json

If you're migrating from appsettings.json configuration:

1. **Set environment variables** with current values
2. **Test email functionality** to ensure it works
3. **Remove sensitive data** from appsettings.json (optional)
4. **Update deployment scripts** to include environment variables
5. **Document the change** for your team

The system will continue to work with appsettings.json as a fallback, so migration can be gradual.
