using Dapper;
using Microsoft.Data.SqlClient;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Implementations
{
    public class EmailTemplateService : IEmailTemplateService
    {
        private readonly string _connectionString;

        public EmailTemplateService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<EmailTemplate?> GetTemplateAsync(string templateName)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT Name, CcEmailAddresses, BccEmailAddresses, Subject, BodyText, BodyHtml
                FROM EmailTemplates 
                WHERE Name = @TemplateName";

            var template = await connection.QueryFirstOrDefaultAsync<EmailTemplate>(sql, new { TemplateName = templateName });

            if (template != null)
            {
                // Load attachments
                var attachmentsSql = @"
                    SELECT ID, TemplateName, AttachmentName, AttachmentPath
                    FROM EmailAttachments 
                    WHERE TemplateName = @TemplateName";

                var attachments = await connection.QueryAsync<EmailAttachment>(attachmentsSql, new { TemplateName = templateName });
                template.Attachments = attachments.ToList();
            }

            return template;
        }

        public async Task<IEnumerable<EmailTemplate>> GetAllTemplatesAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT Name, CcEmailAddresses, BccEmailAddresses, Subject, BodyText, BodyHtml
                FROM EmailTemplates 
                ORDER BY Name";

            var templates = await connection.QueryAsync<EmailTemplate>(sql);

            // Load attachments for each template
            foreach (var template in templates)
            {
                var attachmentsSql = @"
                    SELECT ID, TemplateName, AttachmentName, AttachmentPath
                    FROM EmailAttachments 
                    WHERE TemplateName = @TemplateName";

                var attachments = await connection.QueryAsync<EmailAttachment>(attachmentsSql, new { TemplateName = template.Name });
                template.Attachments = attachments.ToList();
            }

            return templates;
        }

        public async Task<EmailTemplate> CreateTemplateAsync(EmailTemplate template)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO EmailTemplates (Name, CcEmailAddresses, BccEmailAddresses, Subject, BodyText, BodyHtml)
                VALUES (@Name, @CcEmailAddresses, @BccEmailAddresses, @Subject, @BodyText, @BodyHtml)";

            await connection.ExecuteAsync(sql, template);

            return template;
        }

        public async Task<bool> UpdateTemplateAsync(string templateName, EmailTemplate template)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE EmailTemplates 
                SET CcEmailAddresses = @CcEmailAddresses,
                    BccEmailAddresses = @BccEmailAddresses,
                    Subject = @Subject,
                    BodyText = @BodyText,
                    BodyHtml = @BodyHtml
                WHERE Name = @TemplateName";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                TemplateName = templateName,
                template.CcEmailAddresses,
                template.BccEmailAddresses,
                template.Subject,
                template.BodyText,
                template.BodyHtml
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteTemplateAsync(string templateName)
        {
            using var connection = new SqlConnection(_connectionString);

            // Delete attachments first
            var deleteAttachmentsSql = "DELETE FROM EmailAttachments WHERE TemplateName = @TemplateName";
            await connection.ExecuteAsync(deleteAttachmentsSql, new { TemplateName = templateName });

            // Delete template
            var deleteTemplateSql = "DELETE FROM EmailTemplates WHERE Name = @TemplateName";
            var rowsAffected = await connection.ExecuteAsync(deleteTemplateSql, new { TemplateName = templateName });

            return rowsAffected > 0;
        }

        public async Task<IEnumerable<EmailAttachment>> GetTemplateAttachmentsAsync(string templateName)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT ID, TemplateName, AttachmentName, AttachmentPath
                FROM EmailAttachments 
                WHERE TemplateName = @TemplateName
                ORDER BY AttachmentName";

            return await connection.QueryAsync<EmailAttachment>(sql, new { TemplateName = templateName });
        }

        public async Task<EmailAttachment> AddAttachmentAsync(EmailAttachment attachment)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO EmailAttachments (TemplateName, AttachmentName, AttachmentPath)
                OUTPUT INSERTED.ID
                VALUES (@TemplateName, @AttachmentName, @AttachmentPath)";

            var id = await connection.QuerySingleAsync<int>(sql, attachment);
            attachment.ID = id;

            return attachment;
        }

        public async Task<bool> DeleteAttachmentAsync(int attachmentId)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "DELETE FROM EmailAttachments WHERE ID = @AttachmentId";
            var rowsAffected = await connection.ExecuteAsync(sql, new { AttachmentId = attachmentId });

            return rowsAffected > 0;
        }
    }
}
