# Email Service Implementation Documentation

## Overview

The Email Service system provides a comprehensive email functionality for the Shining C Music Studio application. It supports database-driven email templates with placeholder replacement, file attachments, CC/BCC recipients, and both HTML and plain text email formats.

## Architecture

### Components

1. **EmailService** - Core email sending service
2. **EmailTemplateService** - Database template management
3. **EmailController** - API endpoints for sending emails
4. **EmailTemplatesController** - API endpoints for template management
5. **Database Tables** - EmailTemplates and EmailAttachments

### Dependencies

- **SMTP Configuration** - Gmail SMTP settings in appsettings.json
- **Database** - SQL Server for template storage
- **Dapper** - Database access for template operations
- **System.Net.Mail** - .NET email sending functionality

## Database Schema

### EmailTemplates Table

```sql
CREATE TABLE [dbo].[EmailTemplates](
    [Name] [nvarchar](50) NOT NULL,           -- Template identifier (Primary Key)
    [CcEmailAddresses] [nvarchar](256) NULL,  -- Semicolon-separated CC recipients
    [BccEmailAddresses] [nvarchar](256) NULL, -- Semicolon-separated BCC recipients
    [Subject] [nvarchar](500) NULL,           -- Email subject with placeholder support
    [BodyText] [nvarchar](max) NULL,          -- Plain text version
    [BodyHtml] [nvarchar](max) NULL,          -- HTML version
    CONSTRAINT [PK_EmailTemplates] PRIMARY KEY ([Name])
)
```

### EmailAttachments Table

```sql
CREATE TABLE [dbo].[EmailAttachments](
    [ID] [int] IDENTITY(1,1) NOT NULL,        -- Primary Key
    [TemplateName] [nvarchar](50) NOT NULL,   -- Foreign Key to EmailTemplates
    [AttachmentName] [nvarchar](256) NOT NULL, -- Display name for attachment
    [AttachmentPath] [nvarchar](512) NOT NULL, -- File system path
    CONSTRAINT [PK_EmailAttachments] PRIMARY KEY ([ID]),
    CONSTRAINT [FK_EmailAttachments_EmailTemplates] 
        FOREIGN KEY([TemplateName]) REFERENCES [EmailTemplates]([Name]) ON DELETE CASCADE
)
```

## Configuration

### Email Settings Configuration

The EmailService reads configuration from environment variables first, then falls back to appsettings.json.

#### Environment Variables (Recommended for Production)

```bash
# SMTP Configuration
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SENDER_EMAIL=<EMAIL>
EMAIL_SENDER_PASSWORD=evmnvpqszeqovljl
EMAIL_SENDER_NAME=Shining C Music Studio
```

#### Fallback Configuration (appsettings.json)

```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "evmnvpqszeqovljl",
    "SenderName": "Shining C Music Studio"
  }
}
```

**Configuration Priority:**
1. Environment variables (highest priority)
2. appsettings.json (fallback)

### Service Registration (Program.cs)

```csharp
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();
builder.Services.AddScoped<IEmailService, EmailService>();
```

## Core Services

### IEmailService Interface

```csharp
public interface IEmailService
{
    Task<bool> SendScheduleReadyEmailAsync(string tutorEmail, string tutorName);
    Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true);
    Task<bool> SendEmailFromTemplateAsync(string templateName, string toEmail, Dictionary<string, string>? placeholders = null);
}
```

### IEmailTemplateService Interface

```csharp
public interface IEmailTemplateService
{
    Task<EmailTemplate?> GetTemplateAsync(string templateName);
    Task<IEnumerable<EmailTemplate>> GetAllTemplatesAsync();
    Task<EmailTemplate> CreateTemplateAsync(EmailTemplate template);
    Task<bool> UpdateTemplateAsync(string templateName, EmailTemplate template);
    Task<bool> DeleteTemplateAsync(string templateName);
    Task<IEnumerable<EmailAttachment>> GetTemplateAttachmentsAsync(string templateName);
    Task<EmailAttachment> AddAttachmentAsync(EmailAttachment attachment);
    Task<bool> DeleteAttachmentAsync(int attachmentId);
}
```

## Key Features

### 1. Placeholder System

Templates support dynamic content replacement using curly brace syntax:

```html
<h2>Hello {TutorName}!</h2>
<p>Your schedule for {Date} is ready.</p>
```

**Usage:**
```csharp
var placeholders = new Dictionary<string, string>
{
    { "TutorName", "John Smith" },
    { "Date", "July 22, 2025" }
};
await emailService.SendEmailFromTemplateAsync("ScheduleReady", "<EMAIL>", placeholders);
```

### 2. Dual Format Support

Templates can include both HTML and plain text versions:
- **HTML Version** (`BodyHtml`) - Rich formatting with styles
- **Plain Text Version** (`BodyText`) - Fallback for email clients that don't support HTML

### 3. CC/BCC Recipients

Templates support additional recipients:
```sql
-- Semicolon-separated email addresses
CcEmailAddresses = '<EMAIL>;<EMAIL>'
BccEmailAddresses = '<EMAIL>'
```

### 4. File Attachments

Attachments are linked to templates and validated before sending:
```csharp
// Attachment validation
if (File.Exists(attachment.AttachmentPath))
{
    var mailAttachment = new Attachment(attachment.AttachmentPath);
    mailAttachment.Name = attachment.AttachmentName;
    mailMessage.Attachments.Add(mailAttachment);
}
```

### 5. Template Selection Feature

The Tutors page now includes intelligent template selection:

#### Client-Side Implementation
```csharp
// In Tutors.razor.cs
protected async Task SendScheduleEmail(Tutor? tutor)
{
    emailTemplates = await EmailTemplateApi.GetTemplatesAsync();

    if (emailTemplates.Count == 0)
    {
        await DialogService.ShowWarningAsync("No Email Templates",
            "No email templates are available. Please create at least one email template first.");
    }
    else if (emailTemplates.Count == 1)
    {
        // Only one template exists, send it directly
        await SendEmailWithTemplate(tutor, emailTemplates[0].Name);
    }
    else
    {
        // Multiple templates exist, show selection dialog
        showTemplateSelectionModal = true;
    }
}
```

#### Template Selection Modal
- **Dropdown**: Simple string-based dropdown for template names
- **Preview**: Shows selected template subject
- **Validation**: Ensures template is selected before sending
- **State Management**: Preserves selection values before modal closure

#### API Integration
```csharp
// New API service method
public async Task<bool> SendTemplateEmailAsync(int tutorId, string templateName)
{
    var request = new { TemplateName = templateName };
    var response = await _httpClient.PostAsync($"{_baseUrl}/email/send-template/{tutorId}", content);
    return response.IsSuccessStatusCode;
}
```

## API Endpoints

### Email Sending

#### Send Email with Template
```http
POST /api/email/send-template/{tutorId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "templateName": "CustomTemplate"
}
```

#### Send Schedule Ready Email (Legacy)
```http
POST /api/email/send-schedule-ready/{tutorId}
Authorization: Bearer {token}
```

#### Send Custom Email
```http
POST /api/email/send
Authorization: Bearer {token}
Content-Type: application/json

{
  "toEmail": "<EMAIL>",
  "subject": "Custom Subject",
  "body": "Email content",
  "isHtml": true
}
```

### Template Management

#### Get All Templates
```http
GET /api/emailtemplates
Authorization: Bearer {token}
```

#### Get Specific Template
```http
GET /api/emailtemplates/{templateName}
Authorization: Bearer {token}
```

#### Create Template
```http
POST /api/emailtemplates
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "WelcomeEmail",
  "subject": "Welcome {StudentName}!",
  "bodyHtml": "<h1>Welcome {StudentName}!</h1>",
  "bodyText": "Welcome {StudentName}!"
}
```

#### Update Template
```http
PUT /api/emailtemplates/{templateName}
Authorization: Bearer {token}
Content-Type: application/json
```

#### Delete Template
```http
DELETE /api/emailtemplates/{templateName}
Authorization: Bearer {token}
```

### Attachment Management

#### Get Template Attachments
```http
GET /api/emailtemplates/{templateName}/attachments
Authorization: Bearer {token}
```

#### Add Attachment
```http
POST /api/emailtemplates/attachments
Authorization: Bearer {token}
Content-Type: application/json

{
  "templateName": "ScheduleReady",
  "attachmentName": "Schedule.pdf",
  "attachmentPath": "/files/schedules/schedule.pdf"
}
```

#### Delete Attachment
```http
DELETE /api/emailtemplates/attachments/{attachmentId}
Authorization: Bearer {token}
```

## Pre-configured Templates

### ScheduleReady Template

**Purpose:** Notify tutors when their schedule is ready for review

**Placeholders:**
- `{TutorName}` - Tutor's name

**Features:**
- Professional HTML design with Shining C Music Studio branding
- Responsive layout
- Call-to-action link to login portal
- Plain text fallback

## Usage Examples

### 1. Send Schedule Ready Email

```csharp
// In TutorsController or similar
public async Task<IActionResult> NotifyTutorScheduleReady(int tutorId)
{
    var tutor = await _tutorService.GetTutorAsync(tutorId);
    var success = await _emailService.SendScheduleReadyEmailAsync(tutor.Email, tutor.TutorName);
    
    if (success)
        return Ok(new { message = "Email sent successfully" });
    else
        return StatusCode(500, new { message = "Failed to send email" });
}
```

### 2. Send Custom Template Email

```csharp
// Send welcome email to new student
var placeholders = new Dictionary<string, string>
{
    { "StudentName", student.StudentName },
    { "StartDate", student.StartDate.ToString("MMMM dd, yyyy") },
    { "InstructorName", instructor.Name }
};

var success = await _emailService.SendEmailFromTemplateAsync(
    "StudentWelcome", 
    student.Email, 
    placeholders);
```

### 3. Create New Template

```csharp
var template = new EmailTemplate
{
    Name = "LessonReminder",
    Subject = "Lesson Reminder - {LessonDate}",
    BodyHtml = @"
        <h2>Lesson Reminder</h2>
        <p>Dear {StudentName},</p>
        <p>This is a reminder of your upcoming lesson on {LessonDate} at {LessonTime}.</p>
        <p>Location: {Location}</p>
        <p>Instructor: {InstructorName}</p>
    ",
    BodyText = @"
        Lesson Reminder
        
        Dear {StudentName},
        
        This is a reminder of your upcoming lesson on {LessonDate} at {LessonTime}.
        
        Location: {Location}
        Instructor: {InstructorName}
    "
};

await _emailTemplateService.CreateTemplateAsync(template);
```

## Error Handling

### Common Error Scenarios

1. **Template Not Found**
   ```csharp
   _logger.LogError("Email template '{TemplateName}' not found", templateName);
   return false;
   ```

2. **SMTP Configuration Missing**
   ```csharp
   _logger.LogError("Email configuration is incomplete");
   return false;
   ```

3. **Attachment File Missing**
   ```csharp
   _logger.LogWarning("Attachment file not found: {AttachmentPath}", attachment.AttachmentPath);
   ```

4. **Email Sending Failure**
   ```csharp
   _logger.LogError(ex, "Failed to send email to {Email}", toEmail);
   return false;
   ```

### Logging

The service provides comprehensive logging:
- **Information:** Successful email sends
- **Warning:** Missing attachments, empty email addresses
- **Error:** Template not found, SMTP failures, general exceptions

## Security Considerations

1. **Authorization Required** - All API endpoints require valid JWT tokens
2. **Input Validation** - Email addresses and template names are validated
3. **File Path Validation** - Attachment paths are validated before access
4. **SMTP Credentials** - Stored securely in configuration (consider Azure Key Vault for production)

## Performance Considerations

1. **Async Operations** - All database and SMTP operations are asynchronous
2. **Connection Management** - Database connections are properly disposed
3. **Template Caching** - Consider implementing template caching for high-volume scenarios
4. **Attachment Size Limits** - Monitor attachment sizes to prevent memory issues

## Deployment Notes

1. **Database Setup** - Run `Add_Email_Templates.sql` script during deployment
2. **SMTP Configuration** - Ensure Gmail app passwords are configured correctly
3. **File Permissions** - Ensure application has read access to attachment directories
4. **Environment Variables** - Consider using environment variables for sensitive SMTP settings

## Future Enhancements

1. **Template Versioning** - Track template changes over time
2. **Email Queue** - Implement background job processing for high-volume scenarios
3. **Email Analytics** - Track open rates, click-through rates
4. **Template Editor** - Web-based WYSIWYG template editor
5. **Bulk Email** - Support for sending to multiple recipients
6. **Email Scheduling** - Schedule emails for future delivery
