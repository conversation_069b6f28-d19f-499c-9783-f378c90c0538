# Trial and Cancel Functionality - Quick Reference

## 🎯 Quick Overview

The Shining C Music School application now supports marking lessons as **Trial** or **Cancelled** with distinct visual indicators.

## 🎨 Visual Guide

| Status | Color | Icon | What it means |
|--------|-------|------|---------------|
| **Normal** | Tutor's assigned color | None | Regular lesson |
| **Trial** | 🟡 Yellow | ⭐ Star | Trial/demo lesson |
| **Cancelled** | ⚫ Gray | ❌ X | Cancelled lesson |

## 📝 How to Use

### ✅ Mark a Lesson as Trial
1. **Edit** the lesson (click on it in the schedule)
2. **Check** the "Trial Lesson" checkbox ☑️
3. **Save** the lesson
4. **Result**: <PERSON><PERSON> appears in yellow with a star icon ⭐

### ❌ Mark a Lesson as Cancelled  
1. **Edit** the lesson (click on it in the schedule)
2. **Check** the "Cancelled" checkbox ☑️
3. **Save** the lesson
4. **Result**: <PERSON>on appears in gray with an X icon ❌

### 🔄 For Recurring Lessons
- **Single occurrence**: Edit one instance to affect only that lesson
- **Entire series**: Edit the series to affect all future lessons
- **Mixed status**: Each occurrence can have different status

## 🔍 Where You'll See the Status

### 📅 In the Schedule View
- **Color**: Yellow for trial, gray for cancelled
- **Icons**: Star (⭐) for trial, X (❌) for cancelled

### 💬 In Lesson Details Popup
- **Badges**: "Trial" and "Cancelled" badges with icons
- **Color**: Popup header matches lesson color

### ✏️ In the Editor Form
- **Checkboxes**: Clear labels with helpful descriptions
- **Icons**: Visual indicators next to checkbox labels

## ⚠️ Important Notes

### 🎨 Color Priority
1. **Cancelled** lessons are always gray (highest priority)
2. **Trial** lessons are always yellow (if not cancelled)
3. **Normal** lessons use tutor's assigned color
4. **Default** gray if no tutor assigned

### 👨‍🏫 Tutor Color Changes
- Changing a tutor's color **won't affect** trial or cancelled lessons
- Trial and cancelled lessons **keep their special colors**
- Only normal lessons will update to the new tutor color

### 🔒 Permissions
- Only **administrators** can mark lessons as trial or cancelled
- **Tutors and students** can view the status but cannot change it

## 🚀 Quick Actions

### Create a New Trial Lesson
1. Click empty time slot → Fill details → Check "Trial Lesson" → Save

### Convert Regular to Trial
1. Click existing lesson → Check "Trial Lesson" → Save

### Cancel a Lesson
1. Click existing lesson → Check "Cancelled" → Save

### Remove Status
1. Click lesson → Uncheck both boxes → Save

## 🔧 Troubleshooting

### ❓ Lesson not showing correct color?
- **Check**: Is it marked as both trial AND cancelled? (Cancelled wins)
- **Refresh**: Try refreshing the page
- **Permissions**: Ensure you're logged in as administrator

### ❓ Can't see the checkboxes?
- **Login**: Must be logged in as administrator
- **Browser**: Try refreshing or clearing cache

### ❓ Changes not saving?
- **Connection**: Check internet connection
- **Permissions**: Verify administrator access
- **Try again**: Close editor and try again

## 📊 Use Cases

### 🌟 Trial Lessons
- **New student** trying out lessons
- **Demo sessions** for potential students
- **Assessment** lessons to determine skill level
- **Free introductory** sessions

### ❌ Cancelled Lessons
- **Student illness** or emergency
- **Tutor unavailability**
- **Weather-related** cancellations
- **Schedule conflicts**

## 💡 Tips

### 🎯 Best Practices
- Mark trial lessons **before** the session for clear scheduling
- Cancel lessons **as soon as possible** to help with rescheduling
- Use consistent approach across all staff members
- Review trial lesson outcomes regularly

### 📈 Tracking
- Yellow lessons = potential new students (trials)
- Gray lessons = need rescheduling (cancelled)
- Regular colors = confirmed lessons

### 🔄 Workflow Suggestions
1. **Trial Booking**: Mark as trial when scheduling demo
2. **Trial Outcome**: Convert to regular or cancel after session
3. **Cancellations**: Mark cancelled, then reschedule as new lesson
4. **Regular Review**: Check for old cancelled lessons to clean up

## 📞 Support

If you encounter any issues with the trial and cancel functionality:

1. **Check** this quick reference guide
2. **Verify** you have administrator permissions
3. **Try** refreshing the browser
4. **Contact** system administrator if problems persist

---

*This feature enhances lesson management by providing clear visual indicators for different lesson types, making it easier to track trials and handle cancellations effectively.*
