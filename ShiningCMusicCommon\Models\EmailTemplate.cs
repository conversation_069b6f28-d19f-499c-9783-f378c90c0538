using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class EmailTemplate
    {
        [Key]
        [Required]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(256)]
        public string? CcEmailAddresses { get; set; }

        [MaxLength(256)]
        public string? BccEmailAddresses { get; set; }

        [MaxLength(500)]
        public string? Subject { get; set; }

        public string? BodyText { get; set; }

        public string? BodyHtml { get; set; }

        // Navigation property for attachments
        public virtual ICollection<EmailAttachment> Attachments { get; set; } = new List<EmailAttachment>();
    }
}
