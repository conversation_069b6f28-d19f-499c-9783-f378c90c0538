﻿using IdentityServer4.Models;
using IdentityServer4.Stores;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace ShiningCMusicApi.Infrastructure
{
    public class DatabaseClientStore : IClientStore
    {
        private readonly ClientDbContext _context;

        public DatabaseClientStore(ClientDbContext context)
        {
            _context = context;
        }

        public async Task<Client?> FindClientByIdAsync(string clientId)
        {
            var clientEntity = await _context.Clients
                .Include(c => c.AllowedScopes)
                .Include(c => c.AllowedGrantTypes)
                .FirstOrDefaultAsync(c => c.ClientId == clientId);

            if (clientEntity == null) return null;

            await _context.Entry(clientEntity)
                    .Collection(c => c.AllowedGrantTypes)
                    .LoadAsync();
            await _context.Entry(clientEntity)
                    .Collection(c => c.AllowedScopes)
                    .LoadAsync();

            return new Client
            {
                ClientId = clientEntity.ClientId,
                ClientSecrets = new List<Secret> { new Secret(clientEntity.ClientSecret.Sha256()) },
                AllowedGrantTypes = clientEntity.AllowedGrantTypes.Select(g => g.GrantType).ToList(),
                AllowedScopes = clientEntity.AllowedScopes.Select(s => s.Scope).ToList(),
                AccessTokenLifetime = clientEntity.AccessTokenLifetime
            };
        }
    }
}
