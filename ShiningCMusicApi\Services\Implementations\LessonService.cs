using Dapper;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using ShiningCMusicCommon.Models;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.Implementations
{
    public class LessonService : ILessonService
    {
        private readonly string _connectionString;

        public LessonService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<IEnumerable<ScheduleEvent>> GetLessonsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                    l.LessonId as Id,
                    s.StudentName as Subject,
                    --l.StartTime,
                    --l.EndTime,
                    CAST (l.StartTime AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS StartTime,
                    CAST (l.EndTime AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS EndTime,
                    l.Description,
                    loc.Location as Location,
                    l.RecurrenceRule,
                    l.RecurrenceID,
                    l.RecurrenceException,
                    l.TutorId,
                    l.StudentId,
                    l.SubjectId,
                    l.LocationId,
                    t.TutorName,
                    s.StudentName,
                    subj.Subject as SubjectName,
                    loc.Location as LocationName,
                    l.IsTrial,
                    l.IsCancelled
                FROM Lessons l
                LEFT JOIN Tutors t ON l.TutorId = t.TutorId
                LEFT JOIN Students s ON l.StudentId = s.StudentId
                LEFT JOIN Subjects subj ON l.SubjectId = subj.SubjectId
                LEFT JOIN Locations loc ON l.LocationId = loc.LocationId
                WHERE l.IsArchived = 0";

            return await connection.QueryAsync<ScheduleEvent>(sql);
        }

        public async Task<ScheduleEvent?> GetLessonAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                    l.LessonId as Id,
                    s.StudentName as Subject,
                    --l.StartTime,
                    --l.EndTime,
                    CAST (l.StartTime AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS StartTime,
                    CAST (l.EndTime AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS EndTime,
                    l.Description,
                    loc.Location as Location,
                    l.RecurrenceRule,
                    l.RecurrenceID,
                    l.RecurrenceException,
                    l.TutorId,
                    l.StudentId,
                    l.SubjectId,
                    l.LocationId,
                    t.TutorName,
                    s.StudentName,
                    subj.Subject as SubjectName,
                    loc.Location as LocationName,
                    l.IsTrial,
                    l.IsCancelled
                FROM Lessons l
                LEFT JOIN Tutors t ON l.TutorId = t.TutorId
                LEFT JOIN Students s ON l.StudentId = s.StudentId
                LEFT JOIN Subjects subj ON l.SubjectId = subj.SubjectId
                LEFT JOIN Locations loc ON l.LocationId = loc.LocationId
                WHERE l.LessonId = @Id AND l.IsArchived = 0";

            return await connection.QueryFirstOrDefaultAsync<ScheduleEvent>(sql, new { Id = id });
        }

        public async Task<ScheduleEvent> CreateLessonAsync(ScheduleEvent lesson)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Lessons (SubjectId, Description, StartTime, EndTime, TutorId, StudentId, LocationId, RecurrenceRule, RecurrenceID, RecurrenceException, IsRecurring, IsTrial, IsCancelled, CreatedUTC)
                VALUES (@SubjectId, @Description, @StartTime, @EndTime, @TutorId, @StudentId, @LocationId, @RecurrenceRule, @RecurrenceID, @RecurrenceException, @IsRecurring, @IsTrial, @IsCancelled, GETUTCDATE());

                SELECT SCOPE_IDENTITY();";

            var isRecurring = !string.IsNullOrEmpty(lesson.RecurrenceRule);

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                lesson.SubjectId,
                lesson.Description,
                lesson.StartTime,
                lesson.EndTime,
                lesson.TutorId,
                lesson.StudentId,
                lesson.LocationId,
                lesson.RecurrenceRule,
                lesson.RecurrenceID,
                lesson.RecurrenceException,
                IsRecurring = isRecurring,
                lesson.IsTrial,
                lesson.IsCancelled
            });

            lesson.Id = newId;
            return lesson;
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Lessons
                SET SubjectId = @SubjectId,
                    Description = @Description,
                    StartTime = @StartTime,
                    EndTime = @EndTime,
                    TutorId = @TutorId,
                    StudentId = @StudentId,
                    LocationId = @LocationId,
                    RecurrenceRule = @RecurrenceRule,
                    RecurrenceID = @RecurrenceID,
                    RecurrenceException = @RecurrenceException,
                    IsRecurring = @IsRecurring,
                    IsTrial = @IsTrial,
                    IsCancelled = @IsCancelled,
                    UpdatedUTC = GETUTCDATE()
                WHERE LessonId = @Id AND IsArchived = 0";

            var isRecurring = !string.IsNullOrEmpty(lesson.RecurrenceRule);

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                lesson.SubjectId,
                lesson.Description,
                lesson.StartTime,
                lesson.EndTime,
                lesson.TutorId,
                lesson.StudentId,
                lesson.LocationId,
                lesson.RecurrenceRule,
                lesson.RecurrenceID,
                lesson.RecurrenceException,
                IsRecurring = isRecurring,
                lesson.IsTrial,
                lesson.IsCancelled
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Lessons
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE LessonId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<int> PermanentlyDeleteAllLessonsAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            // First, delete non-recurring lessons that are older than retention period
            var nonRecurringSql = @"
                DELETE FROM Lessons
                WHERE (IsRecurring = 0 OR RecurrenceRule IS NULL OR RecurrenceRule = '')
                AND EndTime < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var nonRecurringDeleted = await connection.ExecuteAsync(nonRecurringSql, new { OlderThanDays = olderThanDays });

            // For recurring lessons, we need to calculate the last occurrence date
            // and only delete if that last occurrence + retention days has passed
            var recurringLessonsToCheck = await GetRecurringLessonsForCleanupAsync(connection, olderThanDays);
            var recurringDeleted = 0;

            foreach (var lesson in recurringLessonsToCheck)
            {
                var lastOccurrenceDate = CalculateLastOccurrenceDate(lesson);
                if (lastOccurrenceDate.HasValue)
                {
                    var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
                    if (lastOccurrenceDate.Value < cutoffDate)
                    {
                        // Delete this recurring lesson series
                        var deleteSql = "DELETE FROM Lessons WHERE LessonId = @LessonId";
                        var deleted = await connection.ExecuteAsync(deleteSql, new { LessonId = lesson.LessonId });
                        recurringDeleted += deleted;
                    }
                }
            }

            return nonRecurringDeleted + recurringDeleted;
        }

        public async Task<int> GetAllLessonsCountAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            // Count non-recurring lessons
            var nonRecurringSql = @"
                SELECT COUNT(*)
                FROM Lessons
                WHERE (IsRecurring = 0 OR RecurrenceRule IS NULL OR RecurrenceRule = '')
                AND EndTime < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var nonRecurringCount = await connection.QuerySingleAsync<int>(nonRecurringSql, new { OlderThanDays = olderThanDays });

            // Count recurring lessons that would be eligible for cleanup
            var recurringLessonsToCheck = await GetRecurringLessonsForCleanupAsync(connection, olderThanDays);
            var recurringCount = 0;

            foreach (var lesson in recurringLessonsToCheck)
            {
                var lastOccurrenceDate = CalculateLastOccurrenceDate(lesson);
                if (lastOccurrenceDate.HasValue)
                {
                    var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
                    if (lastOccurrenceDate.Value < cutoffDate)
                    {
                        recurringCount++;
                    }
                }
            }

            return nonRecurringCount + recurringCount;
        }

        private async Task<IEnumerable<Lesson>> GetRecurringLessonsForCleanupAsync(SqlConnection connection, int olderThanDays)
        {
            var sql = @"
                SELECT LessonId, SubjectId, Description, StartTime, EndTime, TutorId, StudentId, LocationId,
                       IsRecurring, RecurrenceRule, RecurrenceID, RecurrenceException, CreatedUTC, UpdatedUTC, IsArchived
                FROM Lessons
                WHERE IsRecurring = 1
                AND RecurrenceRule IS NOT NULL
                AND RecurrenceRule != ''
                AND StartTime < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            return await connection.QueryAsync<Lesson>(sql, new { OlderThanDays = olderThanDays });
        }

        private DateTime? CalculateLastOccurrenceDate(Lesson lesson)
        {
            if (string.IsNullOrEmpty(lesson.RecurrenceRule))
                return null;

            try
            {
                // Parse the recurrence rule to extract COUNT or UNTIL
                var rrule = lesson.RecurrenceRule;

                // Check if it has an UNTIL date
                var untilMatch = Regex.Match(rrule, @"UNTIL=(\d{8}T\d{6}Z?)");
                if (untilMatch.Success)
                {
                    var untilString = untilMatch.Groups[1].Value;
                    if (DateTime.TryParseExact(untilString.Replace("Z", ""), "yyyyMMddTHHmmss", null, System.Globalization.DateTimeStyles.None, out var untilDate))
                    {
                        return untilDate;
                    }
                }

                // Check if it has a COUNT
                var countMatch = Regex.Match(rrule, @"COUNT=(\d+)");
                if (countMatch.Success && int.TryParse(countMatch.Groups[1].Value, out var count))
                {
                    return CalculateLastOccurrenceFromCount(lesson, count);
                }

                // If no COUNT or UNTIL, assume it's an infinite series
                // In this case, we should not delete it based on age alone
                return DateTime.MaxValue;
            }
            catch
            {
                // If we can't parse the recurrence rule, be conservative and don't delete
                return DateTime.MaxValue;
            }
        }

        private DateTime? CalculateLastOccurrenceFromCount(Lesson lesson, int count)
        {
            try
            {
                var rrule = lesson.RecurrenceRule;
                if (string.IsNullOrEmpty(rrule))
                    return null;

                var startTime = lesson.StartTime;
                var duration = lesson.EndTime - lesson.StartTime;

                // Parse frequency and interval
                var freqMatch = Regex.Match(rrule, @"FREQ=(\w+)");
                var intervalMatch = Regex.Match(rrule, @"INTERVAL=(\d+)");

                if (!freqMatch.Success)
                    return null;

                var frequency = freqMatch.Groups[1].Value.ToUpper();
                var interval = intervalMatch.Success ? int.Parse(intervalMatch.Groups[1].Value) : 1;

                DateTime lastOccurrence = startTime;

                switch (frequency)
                {
                    case "DAILY":
                        lastOccurrence = startTime.AddDays((count - 1) * interval);
                        break;
                    case "WEEKLY":
                        lastOccurrence = startTime.AddDays((count - 1) * interval * 7);
                        break;
                    case "MONTHLY":
                        lastOccurrence = startTime.AddMonths((count - 1) * interval);
                        break;
                    case "YEARLY":
                        lastOccurrence = startTime.AddYears((count - 1) * interval);
                        break;
                    default:
                        return null;
                }

                // Return the end time of the last occurrence
                return lastOccurrence.Add(duration);
            }
            catch
            {
                return null;
            }
        }
    }
}
