# Email Templates System - Deployment & Testing Guide

## Overview

This guide provides step-by-step instructions for deploying the Email Templates system to production, including database setup, environment configuration, testing procedures, and troubleshooting.

## 🚀 Pre-Deployment Checklist

### ✅ Code Verification

- [ ] All files committed to source control
- [ ] Build succeeds without errors
- [ ] Unit tests pass
- [ ] Code review completed
- [ ] Documentation updated

### ✅ Database Preparation

- [ ] Database backup created
- [ ] SQL scripts tested on staging
- [ ] Migration scripts prepared
- [ ] Rollback procedures documented

### ✅ Environment Configuration

- [ ] Environment variables defined
- [ ] SMTP settings configured
- [ ] Security certificates updated
- [ ] Connection strings verified

## 🗄️ Database Deployment

### Step 1: Backup Current Database

```sql
-- Create backup before deployment
BACKUP DATABASE [MusicSchool] 
TO DISK = 'C:\Backups\MusicSchool_PreEmailTemplates_20250722.bak'
WITH FORMAT, INIT, COMPRESSION;
```

### Step 2: Execute Database Scripts

```sql
-- Run the email templates setup script
-- Location: ShiningCMusicAPI/SQL/Add_Email_Templates.sql

USE [MusicSchool]
GO

-- <PERSON><PERSON><PERSON> will create:
-- 1. EmailTemplates table
-- 2. EmailAttachments table
-- 3. Foreign key constraints
-- 4. Initial ScheduleReady template
```

### Step 3: Verify Database Setup

```sql
-- Verify tables were created
SELECT TABLE_NAME, TABLE_TYPE 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME IN ('EmailTemplates', 'EmailAttachments');

-- Verify initial template
SELECT Name, Subject, 
       CASE WHEN BodyHtml IS NOT NULL THEN 'Yes' ELSE 'No' END as HasHTML,
       CASE WHEN BodyText IS NOT NULL THEN 'Yes' ELSE 'No' END as HasText
FROM EmailTemplates;

-- Expected result: 1 row with ScheduleReady template
```

### Step 4: Set Database Permissions

```sql
-- Grant permissions to application user
GRANT SELECT, INSERT, UPDATE, DELETE ON EmailTemplates TO [AppUser];
GRANT SELECT, INSERT, UPDATE, DELETE ON EmailAttachments TO [AppUser];
```

## 🔧 Environment Configuration

### Production Environment Variables

#### Azure App Service Configuration

```bash
# Navigate to Azure Portal > App Service > Configuration > Application Settings

# Add these environment variables:
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SENDER_EMAIL=<EMAIL>
EMAIL_SENDER_PASSWORD=evmnvpqszeqovljl
EMAIL_SENDER_NAME=Shining C Music Studio
```

#### Alternative: Azure CLI

```bash
az webapp config appsettings set \
  --resource-group "YourResourceGroup" \
  --name "YourAppName" \
  --settings \
    EMAIL_SMTP_SERVER="smtp.gmail.com" \
    EMAIL_SMTP_PORT="587" \
    EMAIL_SENDER_EMAIL="<EMAIL>" \
    EMAIL_SENDER_PASSWORD="evmnvpqszeqovljl" \
    EMAIL_SENDER_NAME="Shining C Music Studio"
```

### Local Development Setup

#### Windows (PowerShell)

```powershell
$env:EMAIL_SMTP_SERVER="smtp.gmail.com"
$env:EMAIL_SMTP_PORT="587"
$env:EMAIL_SENDER_EMAIL="<EMAIL>"
$env:EMAIL_SENDER_PASSWORD="evmnvpqszeqovljl"
$env:EMAIL_SENDER_NAME="Shining C Music Studio"
```

#### Linux/macOS (Bash)

```bash
export EMAIL_SMTP_SERVER="smtp.gmail.com"
export EMAIL_SMTP_PORT="587"
export EMAIL_SENDER_EMAIL="<EMAIL>"
export EMAIL_SENDER_PASSWORD="evmnvpqszeqovljl"
export EMAIL_SENDER_NAME="Shining C Music Studio"
```

## 📦 Application Deployment

### Step 1: Build Application

```bash
# Navigate to solution root
cd C:\DevOps\repos\ShiningCMusicApp

# Restore packages
dotnet restore

# Build solution
dotnet build --configuration Release

# Publish API
dotnet publish ShiningCMusicAPI -c Release -o ./publish/api

# Publish Client
dotnet publish ShiningCMusicApp -c Release -o ./publish/client
```

### Step 2: Deploy to Azure

#### Option A: Azure DevOps Pipeline

```yaml
# azure-pipelines.yml
trigger:
- main

pool:
  vmImage: 'windows-latest'

steps:
- task: DotNetCoreCLI@2
  displayName: 'Restore packages'
  inputs:
    command: 'restore'
    projects: '**/*.csproj'

- task: DotNetCoreCLI@2
  displayName: 'Build solution'
  inputs:
    command: 'build'
    configuration: 'Release'

- task: DotNetCoreCLI@2
  displayName: 'Publish API'
  inputs:
    command: 'publish'
    projects: 'ShiningCMusicAPI/*.csproj'
    arguments: '--configuration Release --output $(Build.ArtifactStagingDirectory)/api'

- task: DotNetCoreCLI@2
  displayName: 'Publish Client'
  inputs:
    command: 'publish'
    projects: 'ShiningCMusicApp/*.csproj'
    arguments: '--configuration Release --output $(Build.ArtifactStagingDirectory)/client'
```

#### Option B: Manual Deployment

```bash
# Deploy API to Azure App Service
az webapp deployment source config-zip \
  --resource-group "YourResourceGroup" \
  --name "YourAPIAppName" \
  --src "./publish/api.zip"

# Deploy Client to Azure Static Web Apps
az staticwebapp deploy \
  --name "YourClientAppName" \
  --source "./publish/client"
```

### Step 3: Verify Deployment

```bash
# Check API health
curl https://your-api.azurewebsites.net/health

# Check client application
curl https://your-client.azurestaticapps.net

# Test email templates endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
     https://your-api.azurewebsites.net/api/emailtemplates
```

## 🧪 Testing Procedures

### 1. Smoke Tests

#### Database Connectivity

```sql
-- Test database connection
SELECT COUNT(*) FROM EmailTemplates;
-- Expected: 1 (ScheduleReady template)

SELECT COUNT(*) FROM EmailAttachments;
-- Expected: 0 (no attachments initially)
```

#### API Endpoints

```bash
# Test template retrieval
curl -X GET "https://your-api.com/api/emailtemplates" \
     -H "Authorization: Bearer YOUR_TOKEN"

# Expected: 200 OK with ScheduleReady template
```

#### Client Application

```bash
# Test client loading
curl -I https://your-client.com

# Expected: 200 OK
```

### 2. Functional Tests

#### Email Templates CRUD

1. **Navigate to Email Templates page**
   - URL: `/email-templates`
   - Expected: Page loads with template grid

2. **Create new template**
   - Click "Add New Template"
   - Fill in template details
   - Use Rich Text Editor
   - Save template
   - Expected: Template appears in grid

3. **Edit existing template**
   - Click "Edit" on ScheduleReady template
   - Modify content
   - Save changes
   - Expected: Changes reflected in grid

4. **Preview template**
   - Click "Preview" button
   - Expected: Modal shows HTML and text versions

5. **Delete template**
   - Click "Delete" button
   - Confirm deletion
   - Expected: Template removed from grid

#### Email Sending

1. **Send schedule ready email**
   - Navigate to Tutors page
   - Click email button for a tutor
   - Expected: Success message displayed

2. **Verify email delivery**
   - Check recipient's email
   - Expected: Professional email received

### 3. Integration Tests

#### Navigation Testing

1. **Sidebar navigation**
   - Click "Email Templates" in sidebar
   - Expected: Navigate to templates page

2. **Home page card**
   - Click "Manage Templates" card
   - Expected: Navigate to templates page

3. **Mobile navigation**
   - Open hamburger menu
   - Click "Email Templates"
   - Expected: Navigate to templates page

#### Authorization Testing

1. **Admin access**
   - Login as administrator
   - Expected: Can access Email Templates

2. **Non-admin access**
   - Login as tutor/student
   - Expected: Cannot access Email Templates

### 4. Performance Tests

#### Load Testing

```bash
# Test API performance
ab -n 100 -c 10 https://your-api.com/api/emailtemplates

# Expected: Average response time < 500ms
```

#### Database Performance

```sql
-- Test query performance
SET STATISTICS TIME ON;
SELECT * FROM EmailTemplates;
SET STATISTICS TIME OFF;

-- Expected: Query time < 100ms
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Email Templates Not Loading

**Symptoms:**
- Empty grid on Email Templates page
- Console errors about API calls

**Diagnosis:**
```bash
# Check API endpoint
curl -X GET "https://your-api.com/api/emailtemplates" \
     -H "Authorization: Bearer YOUR_TOKEN"

# Check database
SELECT COUNT(*) FROM EmailTemplates;
```

**Solutions:**
- Verify database connection string
- Check API service registration
- Validate authentication token

#### 2. Rich Text Editor Not Working

**Symptoms:**
- Editor doesn't load
- JavaScript errors in console

**Diagnosis:**
```javascript
// Check console for errors
console.log("Syncfusion loaded:", typeof Syncfusion !== 'undefined');
```

**Solutions:**
- Verify Syncfusion package installation
- Check script references in index.html
- Validate component configuration

#### 3. Email Sending Failures

**Symptoms:**
- Error messages when sending emails
- SMTP authentication failures

**Diagnosis:**
```csharp
// Check environment variables
var smtpServer = Environment.GetEnvironmentVariable("EMAIL_SMTP_SERVER");
Console.WriteLine($"SMTP Server: {smtpServer}");
```

**Solutions:**
- Verify environment variables
- Check Gmail app password
- Test SMTP connectivity

#### 4. Navigation Icon Missing

**Symptoms:**
- Blank space where icon should be
- CSS not loading

**Diagnosis:**
```css
/* Check if CSS class exists */
.bi-envelope-fill-nav-menu {
    /* Should have background-image */
}
```

**Solutions:**
- Verify CSS file deployment
- Check SVG encoding
- Clear browser cache

### Rollback Procedures

#### Database Rollback

```sql
-- If issues occur, restore from backup
RESTORE DATABASE [MusicSchool] 
FROM DISK = 'C:\Backups\MusicSchool_PreEmailTemplates_20250722.bak'
WITH REPLACE;
```

#### Application Rollback

```bash
# Revert to previous deployment
az webapp deployment slot swap \
  --resource-group "YourResourceGroup" \
  --name "YourAppName" \
  --slot "staging" \
  --target-slot "production"
```

## 📊 Monitoring

### Key Metrics

1. **Application Performance**
   - Page load times
   - API response times
   - Error rates

2. **Email Functionality**
   - Email send success rate
   - Template usage statistics
   - SMTP connection health

3. **User Adoption**
   - Email Templates page visits
   - Template creation rate
   - Feature usage patterns

### Monitoring Setup

#### Application Insights

```csharp
// Add to Program.cs
builder.Services.AddApplicationInsightsTelemetry();
```

#### Custom Logging

```csharp
// In EmailService
_logger.LogInformation("Email sent successfully to {Email}", toEmail);
_logger.LogError("Failed to send email: {Error}", ex.Message);
```

## 📋 Post-Deployment Checklist

### ✅ Immediate Verification (0-2 hours)

- [ ] Application loads successfully
- [ ] Database tables created
- [ ] Initial template exists
- [ ] Navigation works
- [ ] No critical errors in logs

### ✅ Short-term Verification (2-24 hours)

- [ ] Email sending works
- [ ] Template CRUD operations functional
- [ ] Rich Text Editor working
- [ ] Mobile responsiveness verified
- [ ] Performance within acceptable limits

### ✅ Long-term Monitoring (1-7 days)

- [ ] No memory leaks detected
- [ ] Email delivery rates normal
- [ ] User adoption tracking
- [ ] Error rates within baseline
- [ ] Performance metrics stable

## 📞 Support Contacts

### Technical Issues
- **Database:** DBA Team
- **Infrastructure:** DevOps Team
- **Application:** Development Team

### Business Issues
- **User Training:** Admin Team
- **Feature Requests:** Product Team
- **Bug Reports:** QA Team

---

**Deployment Date:** July 22, 2025  
**Version:** 1.0  
**Status:** Ready for Production  
**Next Review:** August 22, 2025
