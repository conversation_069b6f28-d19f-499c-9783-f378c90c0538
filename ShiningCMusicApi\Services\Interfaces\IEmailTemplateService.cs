using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface IEmailTemplateService
    {
        Task<EmailTemplate?> GetTemplateAsync(string templateName);
        Task<IEnumerable<EmailTemplate>> GetAllTemplatesAsync();
        Task<EmailTemplate> CreateTemplateAsync(EmailTemplate template);
        Task<bool> UpdateTemplateAsync(string templateName, EmailTemplate template);
        Task<bool> DeleteTemplateAsync(string templateName);
        Task<IEnumerable<EmailAttachment>> GetTemplateAttachmentsAsync(string templateName);
        Task<EmailAttachment> AddAttachmentAsync(EmailAttachment attachment);
        Task<bool> DeleteAttachmentAsync(int attachmentId);
    }
}
