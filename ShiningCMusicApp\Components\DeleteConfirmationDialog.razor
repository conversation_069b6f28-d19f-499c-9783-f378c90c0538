@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons

<SfDialog @bind-Visible="IsVisible" Header="@Title" Width="400px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="false" CssClass="delete-confirmation-dialog">
    <DialogEvents Closed="OnDialogClosed"></DialogEvents>
    <DialogTemplates>
        <Content>
            <div class="delete-confirmation-content">
                <div class="d-flex align-items-start mb-3">
                    <i class="bi bi-exclamation-triangle-fill text-warning me-3" style="font-size: 2rem;"></i>
                    <div>
                        <p class="mb-2 fw-semibold">@Message</p>
                        @if (!string.IsNullOrEmpty(Details))
                        {
                            <p class="text-muted small mb-0">@Details</p>
                        }
                    </div>
                </div>
            </div>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton OnClick="OnConfirmClick" Content="@ConfirmButtonText" CssClass="btn btn-danger me-2" IsPrimary="false"></DialogButton>
        <DialogButton OnClick="OnCancelClick" Content="@CancelButtonText" CssClass="btn btn-cancel-custom"></DialogButton>
    </DialogButtons>
</SfDialog>
