# Email Service System - README

## Overview

The Email Service system for Shining C Music Studio provides a comprehensive, database-driven email solution with template management, placeholder replacement, and file attachment support. This system was implemented to move email content from hardcoded templates to a flexible database-driven approach.

## 🚀 Quick Start

### 1. Database Setup
```bash
# Run the database setup script
sqlcmd -S your-server -d MusicSchool -i "SQL/Add_Email_Templates.sql"
```

### 2. Send Your First Email
```csharp
// Inject the service
[Inject] protected IEmailService EmailService { get; set; }

// Send schedule ready email
var success = await EmailService.SendScheduleReadyEmailAsync("<EMAIL>", "<PERSON>");
```

### 3. Use in UI (Blazor)
```csharp
// In your page code-behind
protected async Task SendEmail(Tutor tutor)
{
    var success = await EmailApi.SendScheduleReadyEmailAsync(tutor.TutorId);
    if (success)
        await DialogService.ShowSuccessAsync("Email sent!");
}
```

## 📁 Documentation Structure

### API Documentation

| Document | Purpose | Audience |
|----------|---------|----------|
| **EmailService_Implementation.md** | Complete technical documentation | Developers, Architects |
| **EmailService_QuickReference.md** | Quick usage guide and examples | Developers |
| **EmailService_DatabaseSetup.md** | Database setup and maintenance | DBAs, DevOps |
| **EmailService_EnvironmentVariables.md** | Environment configuration guide | DevOps, Developers |
| **EmailService_README.md** | Overview and getting started | All stakeholders |

### Client Documentation

| Document | Purpose | Audience |
|----------|---------|----------|
| **EmailTemplates_Complete_Implementation.md** | Full system implementation guide | Developers, Architects |
| **HomePage_EmailTemplates_Integration.md** | Home page integration details | UI/UX Developers |
| **Navigation_EmailTemplates_Integration.md** | Navigation system updates | Frontend Developers |
| **EmailTemplates_Deployment_Guide.md** | Deployment and testing procedures | DevOps, QA Teams |

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Blazor UI     │───▶│   API Layer      │───▶│   Database      │
│                 │    │                  │    │                 │
│ • Email Button  │    │ • EmailService   │    │ • EmailTemplates│
│ • Dialog Msgs   │    │ • TemplateService│    │ • Attachments   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   SMTP Server    │
                       │   (Gmail)        │
                       └──────────────────┘
```

## 🔧 Key Components

### Services
- **EmailService** - Core email sending functionality
- **EmailTemplateService** - Database template management
- **EmailApiService** - Client-side API wrapper

### Controllers
- **EmailController** - Email sending endpoints
- **EmailTemplatesController** - Template management endpoints

### Models
- **EmailTemplate** - Template entity with placeholders
- **EmailAttachment** - File attachment entity

### Database Tables
- **EmailTemplates** - Template storage
- **EmailAttachments** - Attachment references

## ✨ Key Features

### 🎯 Template-Based Emails
- Database-stored templates with HTML and plain text versions
- Dynamic placeholder replacement (`{TutorName}`, `{LessonDate}`, etc.)
- CC/BCC recipient support
- File attachment support

### 📧 Email Types
- **Schedule Ready** - Notify tutors when schedule is updated
- **Custom Templates** - Extensible system for any email type
- **Direct Emails** - Send without templates for one-off messages

### 🔒 Security & Reliability
- JWT authentication required for all endpoints
- Comprehensive error handling and logging
- Input validation and sanitization
- File path validation for attachments

### 📱 UI Integration
- Email button in Tutors page with envelope icon
- Success/error dialog feedback
- Responsive button layout (4 buttons with grid-btn-fourth)

## 🎨 Pre-configured Templates

### ScheduleReady Template
- **Purpose:** Notify tutors when their schedule is ready
- **Placeholders:** `{TutorName}`
- **Features:** Professional HTML design, responsive layout, studio branding
- **Usage:** Automatically used by "Email" button in Tutors page

## 🔧 Configuration

### Configuration Settings

#### Environment Variables (Production Recommended)
```bash
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SENDER_EMAIL=<EMAIL>
EMAIL_SENDER_PASSWORD=evmnvpqszeqovljl
EMAIL_SENDER_NAME=Shining C Music Studio
```

#### Fallback Settings (appsettings.json)
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "evmnvpqszeqovljl",
    "SenderName": "Shining C Music Studio"
  }
}
```

**Configuration reads in this order:**
1. Environment variables (highest priority)
2. appsettings.json (fallback)

### Service Registration (Program.cs)
```csharp
// API Services
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();
builder.Services.AddScoped<IEmailService, EmailService>();

// Client Services  
builder.Services.AddScoped<IEmailApiService, EmailApiService>();
```

## 🚀 Usage Examples

### Send Schedule Ready Email (Legacy)
```csharp
// From controller - uses hardcoded "ScheduleReady" template
var success = await _emailService.SendScheduleReadyEmailAsync(tutor.Email, tutor.TutorName);

// From Blazor page - uses hardcoded "ScheduleReady" template
var success = await EmailApi.SendScheduleReadyEmailAsync(tutor.TutorId);
```

### Send Email with Template Selection (Recommended)
```csharp
// From Blazor page - automatically handles template selection
// - If 0 templates: Shows warning
// - If 1 template: Sends automatically
// - If 2+ templates: Shows selection dialog
await SendScheduleEmail(tutor); // Called from Tutors page email button
```

### Send Email with Specific Template
```csharp
// From Blazor page - send with specific template name
var success = await EmailApi.SendTemplateEmailAsync(tutor.TutorId, "CustomTemplate");

// From controller - send with specific template and placeholders
var placeholders = new Dictionary<string, string>
{
    { "StudentName", "Jane Doe" },
    { "LessonDate", "July 25, 2025" },
    { "InstructorName", "Mr. Smith" }
};

var success = await _emailService.SendEmailFromTemplateAsync(
    "LessonReminder",
    "<EMAIL>",
    placeholders);
```

### Create New Template
```csharp
var template = new EmailTemplate
{
    Name = "WelcomeEmail",
    Subject = "Welcome {StudentName}!",
    BodyHtml = "<h1>Welcome {StudentName} to our studio!</h1>",
    BodyText = "Welcome {StudentName} to our studio!"
};

await _emailTemplateService.CreateTemplateAsync(template);
```

## 🎯 Template Selection Feature

The email system now includes intelligent template selection when sending emails from the Tutors page:

### How It Works
1. **User clicks email button** for a tutor
2. **System checks available templates**:
   - **0 templates**: Shows warning "No email templates available"
   - **1 template**: Automatically sends email using that template
   - **2+ templates**: Opens template selection dialog

### Template Selection Dialog
- **Dropdown list** showing all available template names
- **Subject preview** displays when a template is selected
- **Send/Cancel buttons** with loading states
- **Validation** ensures a template is selected before sending

### Benefits
- **Flexible**: Supports any number of email templates
- **User-friendly**: Automatic selection when only one template exists
- **Consistent**: Uses the same template management system
- **Backward compatible**: Still works with existing "ScheduleReady" template

### Implementation Details
```csharp
// Template selection logic in Tutors page
protected async Task SendScheduleEmail(Tutor? tutor)
{
    emailTemplates = await EmailTemplateApi.GetTemplatesAsync();

    if (emailTemplates.Count == 0)
        // Show warning
    else if (emailTemplates.Count == 1)
        // Send directly with single template
    else
        // Show template selection modal
}
```

## 🔍 API Endpoints

### Email Operations
```http
POST /api/email/send-schedule-ready/{tutorId}  # Send schedule ready (legacy)
POST /api/email/send-template/{tutorId}        # Send with specific template
POST /api/email/send                           # Send custom email
```

### Template Management
```http
GET    /api/emailtemplates                     # List all templates
GET    /api/emailtemplates/{name}              # Get specific template
POST   /api/emailtemplates                     # Create template
PUT    /api/emailtemplates/{name}              # Update template
DELETE /api/emailtemplates/{name}              # Delete template
```

### Attachment Management
```http
GET    /api/emailtemplates/{name}/attachments  # Get attachments
POST   /api/emailtemplates/attachments         # Add attachment
DELETE /api/emailtemplates/attachments/{id}    # Delete attachment
```

## 🎯 UI Changes

### Tutors Page Updates
- ✅ Added email button with `bi-envelope` icon
- ✅ Positioned before "Lessons" button as requested
- ✅ Changed all 4 buttons to use `grid-btn-fourth` class
- ✅ Increased Actions column width to accommodate 4 buttons
- ✅ Added email functionality with success/error dialogs

### Button Layout
```html
<!-- 4 buttons with equal width -->
<button class="btn btn-outline-info btn-sm grid-action-btn grid-btn-fourth">Email</button>
<button class="btn btn-outline-success btn-sm grid-action-btn grid-btn-fourth">Lessons</button>
<button class="btn btn-outline-primary btn-sm grid-action-btn grid-btn-fourth">Edit</button>
<button class="btn btn-outline-danger btn-sm grid-action-btn grid-btn-fourth">Delete</button>
```

## 📊 Monitoring & Logging

### Log Levels
- **Information:** Successful email sends
- **Warning:** Missing attachments, empty email addresses
- **Error:** Template not found, SMTP failures, exceptions

### Key Metrics to Monitor
- Email send success rate
- Template usage frequency
- SMTP connection failures
- Attachment file access errors

## 🔧 Troubleshooting

### Common Issues

| Issue | Cause | Solution |
|-------|-------|----------|
| Email not sending | SMTP configuration | Check appsettings.json EmailSettings |
| Template not found | Missing database record | Run Add_Email_Templates.sql |
| Placeholders not replacing | Incorrect syntax | Use `{PlaceholderName}` format |
| Attachment not found | Invalid file path | Verify file exists and permissions |

### Debug Steps
1. Check application logs for detailed error messages
2. Verify database connection and template existence
3. Test SMTP settings with external tool
4. Validate email addresses and placeholder values

## 🚀 Deployment Checklist

### Database
- [ ] Run `Add_Email_Templates.sql` script
- [ ] Verify tables created successfully
- [ ] Check initial ScheduleReady template exists

### Configuration
- [ ] Set environment variables for production (EMAIL_SMTP_SERVER, EMAIL_SENDER_EMAIL, etc.)
- [ ] Update EmailSettings in appsettings.json as fallback
- [ ] Configure Gmail app password
- [ ] Test SMTP connectivity

### Application
- [ ] Deploy updated API with email services
- [ ] Deploy updated Blazor app with email button
- [ ] Verify service registrations in Program.cs

### Testing
- [ ] Test email button in Tutors page
- [ ] Verify email delivery
- [ ] Check success/error dialogs
- [ ] Test template management endpoints

## 🔮 Future Enhancements

### Planned Features
- **Template Editor** - Web-based WYSIWYG editor
- **Email Queue** - Background processing for high volume
- **Analytics** - Track open rates and engagement
- **Bulk Email** - Send to multiple recipients
- **Scheduling** - Schedule emails for future delivery

### Potential Templates
- **Lesson Reminders** - Automated student notifications
- **Welcome Emails** - New student onboarding
- **Payment Reminders** - Billing notifications
- **Event Announcements** - Studio events and recitals

## 📞 Support

### Documentation
- **[Template Selection Guide](EmailTemplateSelection_Guide.md)** - Comprehensive guide to the template selection feature
- **[Change Log](EmailTemplateSelection_ChangeLog.md)** - Detailed list of all changes made for template selection
- **[Quick Reference](EmailService_QuickReference.md)** - Quick reference for common operations
- **[Implementation Details](EmailService_Implementation.md)** - Technical implementation details
- Check API documentation via Swagger UI
- Review database schema in setup guide

### Contact
- Technical issues: Check application logs and error messages
- Feature requests: Document requirements and use cases
- Database issues: Consult database setup guide

---

**Version:** 1.1 (Template Selection Feature)
**Last Updated:** July 23, 2025
**Author:** Shining C Music Studio Development Team
