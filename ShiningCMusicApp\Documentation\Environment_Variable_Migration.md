# Environment Variable Migration Guide

## Overview

The Shining C Music App has updated its environment variable naming convention for better clarity and standardization. This document outlines the changes and migration steps required.

## Changes Made

### Environment Variable Rename
- **Old Name**: `ConnectionStrings_MusicSchool`
- **New Name**: `DATABASE_CONNECTION_STRING`

### Rationale
- **Clarity**: More descriptive name that clearly indicates its purpose
- **Standardization**: Follows common naming conventions for database connection strings
- **Consistency**: Aligns with other environment variables in the application

## Migration Required

### Azure App Service
If you have deployed the application to Azure App Service, you need to update the environment variable:

1. **Navigate to Azure Portal**
2. **Go to your App Service**
3. **Select Configuration → Application Settings**
4. **Update the environment variable**:
   - Remove: `ConnectionStrings_MusicSchool`
   - Add: `DATABASE_CONNECTION_STRING` with the same connection string value
5. **Save and restart the App Service**

### Local Development
For local development, no changes are needed as the application still falls back to the `appsettings.json` configuration file.

### Docker/Container Deployments
If using Docker or other container deployments, update your environment variable configuration:

```bash
# Old
-e ConnectionStrings_MusicSchool="Server=..."

# New
-e DATABASE_CONNECTION_STRING="Server=..."
```

### CI/CD Pipelines
Update any CI/CD pipeline configurations that set this environment variable:

```yaml
# Example Azure DevOps Pipeline
variables:
  # Old
  # ConnectionStrings_MusicSchool: $(DatabaseConnectionString)
  
  # New
  DATABASE_CONNECTION_STRING: $(DatabaseConnectionString)
```

## Files Updated

The following files have been updated to use the new environment variable name:

### Code Files
- `ShiningCMusicApi/Program.cs`
- `ShiningCMusicApi/Services/Implementations/UserService.cs`
- `ShiningCMusicApi/Services/Implementations/StudentService.cs`
- `ShiningCMusicApi/Services/Implementations/LessonService.cs`
- `ShiningCMusicApi/Services/Implementations/SubjectService.cs`
- `ShiningCMusicApi/Services/Implementations/TutorService.cs`
- `ShiningCMusicApi/Services/Implementations/LocationService.cs`

### Deployment Scripts
- `deploy-azure.ps1`
- `deploy-simple.bat`
- `deploy-to-existing-azure.ps1`

### Documentation
- `ShiningCMusicApp/README.md`
- `ShiningCMusicApi/Documentation/REFACTORING_SUMMARY.md`
- `azure-deployment-guide.md`

## Verification

After migration, verify the application works correctly:

1. **Check Application Logs**: Ensure no database connection errors
2. **Test Database Operations**: Verify CRUD operations work
3. **Monitor Performance**: Confirm no performance degradation

## Rollback Plan

If issues occur, you can temporarily rollback by:

1. **Keeping both environment variables** during transition:
   ```bash
   DATABASE_CONNECTION_STRING="your-connection-string"
   ConnectionStrings_MusicSchool="your-connection-string"  # Temporary fallback
   ```

2. **The application will prioritize the new variable** but fall back to the old one if needed

## Support

If you encounter issues during migration:

1. Check Azure App Service logs for connection errors
2. Verify the connection string format is correct
3. Ensure the database server is accessible
4. Contact the development team for assistance

## Timeline

- **Immediate**: Code changes deployed
- **Grace Period**: Both old and new variables supported temporarily
- **Future Release**: Old variable support will be removed

**Action Required**: Update your environment variables as soon as possible to avoid disruption.
