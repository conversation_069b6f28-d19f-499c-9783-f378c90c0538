# Email Template Selection Feature Guide

## Overview

The Email Template Selection feature provides intelligent template selection when sending emails from the Tutors page. This feature automatically adapts based on the number of available email templates, providing an optimal user experience.

## How It Works

### Template Count Logic

| Template Count | Behavior |
|----------------|----------|
| **0 templates** | Shows warning message: "No email templates available" |
| **1 template** | Automatically sends email using that template |
| **2+ templates** | Opens template selection dialog |

### User Experience Flow

1. **User clicks email button** for a tutor on the Tutors page
2. **System loads available templates** from the database
3. **System applies logic** based on template count:
   - **No templates**: Warning dialog appears
   - **Single template**: Email sent immediately with success message
   - **Multiple templates**: Selection modal opens

## Template Selection Modal

### Features
- **Template dropdown**: Lists all available template names
- **Subject preview**: Shows the subject line of the selected template
- **Validation**: Prevents sending without template selection
- **Loading states**: Visual feedback during email sending
- **Responsive design**: Works on desktop and mobile

### UI Components
```html
<SfDropDownList TValue="string" TItem="string" @bind-Value="selectedTemplateName"
                DataSource="@GetTemplateNames()" Placeholder="Choose a template">
</SfDropDownList>
```

### Validation
- Template selection is required before sending
- Empty template names are rejected
- User-friendly error messages for all failure scenarios

## Technical Implementation

### Client-Side (Blazor)

#### Main Logic
```csharp
protected async Task SendScheduleEmail(Tutor? tutor)
{
    // Load available email templates
    emailTemplates = await EmailTemplateApi.GetTemplatesAsync();

    if (emailTemplates.Count == 0)
    {
        await DialogService.ShowWarningAsync("No Email Templates", 
            "No email templates are available. Please create at least one email template first.");
    }
    else if (emailTemplates.Count == 1)
    {
        // Only one template exists, send it directly
        await SendEmailWithTemplate(tutor, emailTemplates[0].Name);
    }
    else
    {
        // Multiple templates exist, show selection dialog
        currentEmailTutor = tutor;
        templateSelectionModalTitle = $"Select Email Template for {tutor.TutorName}";
        selectedTemplateName = "";
        showTemplateSelectionModal = true;
    }
}
```

#### Template Selection Handler
```csharp
protected async Task SendSelectedTemplate()
{
    if (string.IsNullOrWhiteSpace(selectedTemplateName))
    {
        await DialogService.ShowWarningAsync("Template Selection Required", 
            "Please select a template before sending the email.");
        return;
    }

    // Store values before closing modal to prevent data loss
    var tutorToEmail = currentEmailTutor;
    var templateToUse = selectedTemplateName;
    
    CloseTemplateSelectionModal();
    await SendEmailWithTemplate(tutorToEmail, templateToUse);
}
```

### Server-Side (API)

#### New Endpoint
```csharp
[HttpPost("send-template/{tutorId}")]
public async Task<IActionResult> SendTemplateEmail(int tutorId, [FromBody] SendTemplateEmailRequest request)
{
    var tutor = await _tutorService.GetTutorAsync(tutorId);
    var placeholders = new Dictionary<string, string>
    {
        { "TutorName", tutor.TutorName ?? "Tutor" }
    };

    var success = await _emailService.SendEmailFromTemplateAsync(request.TemplateName, tutor.Email, placeholders);
    return success ? Ok() : StatusCode(500);
}
```

#### Request Model
```csharp
public class SendTemplateEmailRequest
{
    public string TemplateName { get; set; } = string.Empty;
}
```

## Benefits

### For Users
- **Simplified workflow**: No need to remember template names
- **Automatic selection**: When only one template exists
- **Visual feedback**: See template subjects before sending
- **Error prevention**: Validation prevents mistakes

### For Administrators
- **Flexible**: Supports any number of templates
- **Maintainable**: Uses existing template management system
- **Backward compatible**: Works with existing "ScheduleReady" template
- **Extensible**: Easy to add new templates through Email Templates page

## Best Practices

### Template Management
1. **Create meaningful template names**: Use descriptive names like "WeeklySchedule", "LessonReminder"
2. **Include clear subjects**: Help users identify the right template
3. **Test templates**: Preview templates before using them for emails
4. **Maintain templates**: Regularly review and update template content

### Usage Guidelines
1. **Single template scenario**: Perfect for organizations with standardized communications
2. **Multiple template scenario**: Ideal for organizations with different email types
3. **Template naming**: Use consistent naming conventions for easy identification

## Troubleshooting

### Common Issues

**Template not appearing in dropdown**
- Verify template exists in Email Templates page
- Check template name is not empty
- Refresh the page to reload templates

**Email not sending**
- Verify tutor has email address configured
- Check template content is not empty
- Verify email service configuration

**Modal not closing**
- Check for JavaScript errors in browser console
- Verify all required fields are filled

### Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "No email templates available" | No templates in database | Create templates via Email Templates page |
| "Template Selection Required" | No template selected | Select a template from dropdown |
| "No Email Address" | Tutor missing email | Add email address to tutor profile |
| "Email Failed" | SMTP/API error | Check email service configuration |

## Migration Notes

### From Legacy System
- Existing "ScheduleReady" template continues to work
- No changes required for single-template setups
- Multiple templates automatically enable selection dialog

### API Changes
- New endpoint: `POST /api/email/send-template/{tutorId}`
- Legacy endpoint still available: `POST /api/email/send-schedule-ready/{tutorId}`
- New client service method: `SendTemplateEmailAsync(int tutorId, string templateName)`

## Future Enhancements

### Planned Features
- Template categories for better organization
- Template preview in selection modal
- Bulk email sending with template selection
- Template usage analytics
- Custom placeholder management per template

### Integration Opportunities
- Student email notifications with template selection
- Automated email scheduling with template rotation
- Template-based email campaigns
- Integration with calendar events for context-aware template selection
