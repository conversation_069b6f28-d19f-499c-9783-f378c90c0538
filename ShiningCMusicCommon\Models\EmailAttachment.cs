using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ShiningCMusicCommon.Models
{
    public class EmailAttachment
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Required]
        [MaxLength(50)]
        public string TemplateName { get; set; } = string.Empty;

        [Required]
        [MaxLength(256)]
        public string AttachmentName { get; set; } = string.Empty;

        [Required]
        [MaxLength(512)]
        public string AttachmentPath { get; set; } = string.Empty;

        // Navigation property
        [ForeignKey(nameof(TemplateName))]
        public virtual EmailTemplate? Template { get; set; }
    }
}
