using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public class EmailTemplateApiService : IEmailTemplateApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthenticationService _authService;
        private readonly string _baseUrl;

        public EmailTemplateApiService(HttpClient httpClient, IAuthenticationService authService, ApiConfiguration apiConfig)
        {
            _httpClient = httpClient;
            _authService = authService;
            _baseUrl = apiConfig.BaseUrl;
        }

        private async Task<bool> SetAuthorizationHeaderAsync()
        {
            var token = await _authService.GetAccessTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                return true;
            }
            return false;
        }

        public async Task<List<EmailTemplate>> GetTemplatesAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<EmailTemplate>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/emailtemplates");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var templates = JsonSerializer.Deserialize<List<EmailTemplate>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return templates ?? new List<EmailTemplate>();
                }

                return new List<EmailTemplate>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting email templates: {ex.Message}");
                return new List<EmailTemplate>();
            }
        }

        public async Task<EmailTemplate?> GetTemplateAsync(string templateName)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/emailtemplates/{templateName}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<EmailTemplate>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting email template: {ex.Message}");
                return null;
            }
        }

        public async Task<EmailTemplate?> CreateTemplateAsync(EmailTemplate template)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var json = JsonSerializer.Serialize(template);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/emailtemplates", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<EmailTemplate>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating email template: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UpdateTemplateAsync(string templateName, EmailTemplate template)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var json = JsonSerializer.Serialize(template);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"{_baseUrl}/emailtemplates/{templateName}", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating email template: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteTemplateAsync(string templateName)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/emailtemplates/{templateName}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting email template: {ex.Message}");
                return false;
            }
        }

        public async Task<List<EmailAttachment>> GetTemplateAttachmentsAsync(string templateName)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<EmailAttachment>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/emailtemplates/{templateName}/attachments");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var attachments = JsonSerializer.Deserialize<List<EmailAttachment>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return attachments ?? new List<EmailAttachment>();
                }

                return new List<EmailAttachment>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting template attachments: {ex.Message}");
                return new List<EmailAttachment>();
            }
        }

        public async Task<EmailAttachment?> AddAttachmentAsync(EmailAttachment attachment)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var json = JsonSerializer.Serialize(attachment);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/emailtemplates/attachments", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<EmailAttachment>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding attachment: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> DeleteAttachmentAsync(int attachmentId)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/emailtemplates/attachments/{attachmentId}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting attachment: {ex.Message}");
                return false;
            }
        }
    }
}
