using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public interface IEmailTemplateApiService
    {
        Task<List<EmailTemplate>> GetTemplatesAsync();
        Task<EmailTemplate?> GetTemplateAsync(string templateName);
        Task<EmailTemplate?> CreateTemplateAsync(EmailTemplate template);
        Task<bool> UpdateTemplateAsync(string templateName, EmailTemplate template);
        Task<bool> DeleteTemplateAsync(string templateName);
        Task<List<EmailAttachment>> GetTemplateAttachmentsAsync(string templateName);
        Task<EmailAttachment?> AddAttachmentAsync(EmailAttachment attachment);
        Task<bool> DeleteAttachmentAsync(int attachmentId);
    }
}
