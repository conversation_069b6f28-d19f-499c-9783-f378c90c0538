# Changelog

All notable changes to the Shining C Music School application will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Trial and Cancel Functionality** - Major new feature for lesson management
  - Added ability to mark lessons as "Trial" or "Cancelled"
  - Trial lessons display in yellow color with star icon (⭐)
  - Cancelled lessons display in gray color with X icon (❌)
  - Color override system: Cancelled > Trial > Tutor Color > Default
  - Visual indicators in schedule view, event templates, and QuickInfo popups
  - Checkboxes in event editor form for easy status selection
  - Support for both single events and recurring series
  - Smart tutor color management (trial/cancelled lessons maintain special colors)
  - Comprehensive documentation and user guides

### Database Changes
- Added `IsTrial` bit column to Lessons table (default: false)
- Added `IsCancelled` bit column to Lessons table (default: false)
- Updated migration script with safety checks for existing installations

### Technical Improvements
- Enhanced Lesson and ScheduleEvent models with new boolean properties
- Updated LessonService with full CRUD support for new fields
- Improved color assignment logic with priority system
- Added visual status indicators throughout the UI
- Maintained backward compatibility with existing data

### Documentation
- Created comprehensive feature documentation
- Added quick reference guide for users
- Provided technical implementation summary for developers
- Updated API documentation to reflect new fields

## Previous Versions

### [1.0.0] - Initial Release
- Basic lesson scheduling functionality
- User authentication and authorization
- Tutor and student management
- Subject and location management
- Recurring event support
- Responsive design for mobile and desktop
- Admin dashboard and reporting

---

## Legend

- **Added** for new features
- **Changed** for changes in existing functionality  
- **Deprecated** for soon-to-be removed features
- **Removed** for now removed features
- **Fixed** for any bug fixes
- **Security** for vulnerability fixes
