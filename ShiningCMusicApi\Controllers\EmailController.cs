using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EmailController : ControllerBase
    {
        private readonly IEmailService _emailService;
        private readonly ITutorService _tutorService;
        private readonly ILogger<EmailController> _logger;

        public EmailController(
            IEmailService emailService,
            ITutorService tutorService,
            ILogger<EmailController> logger)
        {
            _emailService = emailService;
            _tutorService = tutorService;
            _logger = logger;
        }

        // POST: api/email/send-schedule-ready/{tutorId}
        [HttpPost("send-schedule-ready/{tutorId}")]
        public async Task<IActionResult> SendScheduleReadyEmail(int tutorId)
        {
            try
            {
                var tutor = await _tutorService.GetTutorAsync(tutorId);
                if (tutor == null)
                {
                    return NotFound(new { message = "Tutor not found" });
                }

                if (string.IsNullOrWhiteSpace(tutor.Email))
                {
                    return BadRequest(new { message = "Tu<PERSON> does not have an email address" });
                }

                var success = await _emailService.SendScheduleReadyEmailAsync(tutor.Email, tutor.TutorName ?? "Tutor");
                if (success)
                {
                    return Ok(new { message = $"Schedule ready email sent to {tutor.TutorName}" });
                }
                else
                {
                    return StatusCode(500, new { message = "Failed to send email" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending schedule ready email");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: api/email/send
        [HttpPost("send")]
        public async Task<IActionResult> SendEmail([FromBody] SendEmailRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ToEmail))
                {
                    return BadRequest(new { message = "Recipient email is required" });
                }

                if (string.IsNullOrWhiteSpace(request.Subject))
                {
                    return BadRequest(new { message = "Email subject is required" });
                }

                if (string.IsNullOrWhiteSpace(request.Body))
                {
                    return BadRequest(new { message = "Email body is required" });
                }

                var success = await _emailService.SendEmailAsync(
                    request.ToEmail,
                    request.Subject,
                    request.Body,
                    request.IsHtml);

                if (success)
                {
                    return Ok(new { message = $"Email sent to {request.ToEmail}" });
                }
                else
                {
                    return StatusCode(500, new { message = "Failed to send email" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: api/email/send-template/{tutorId}
        [HttpPost("send-template/{tutorId}")]
        public async Task<IActionResult> SendTemplateEmail(int tutorId, [FromBody] SendTemplateEmailRequest request)
        {
            try
            {
                var tutor = await _tutorService.GetTutorAsync(tutorId);
                if (tutor == null)
                {
                    return NotFound(new { message = "Tutor not found" });
                }

                if (string.IsNullOrWhiteSpace(tutor.Email))
                {
                    return BadRequest(new { message = "Tutor does not have an email address" });
                }

                if (string.IsNullOrWhiteSpace(request.TemplateName))
                {
                    return BadRequest(new { message = "Template name is required" });
                }

                var placeholders = new Dictionary<string, string>
                {
                    { "TutorName", tutor.TutorName ?? "Tutor" }
                };

                var success = await _emailService.SendEmailFromTemplateAsync(request.TemplateName, tutor.Email, placeholders);
                if (success)
                {
                    return Ok(new { message = $"Email sent to {tutor.TutorName} using template '{request.TemplateName}'" });
                }
                else
                {
                    return StatusCode(500, new { message = "Failed to send email" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending template email");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }

    public class SendEmailRequest
    {
        public string ToEmail { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IsHtml { get; set; } = true;
    }

    public class SendTemplateEmailRequest
    {
        public string TemplateName { get; set; } = string.Empty;
    }
}
