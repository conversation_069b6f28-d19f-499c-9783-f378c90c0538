﻿using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicApi.Infrastructure
{
    public class ClientEntity
    {
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public int AccessTokenLifetime { get; set; }
        public List<ClientScopeEntity> AllowedScopes { get; set; } = new List<ClientScopeEntity>();
        public List<ClientGrantTypeEntity> AllowedGrantTypes { get; set; } = new List<ClientGrantTypeEntity>();
    }

    public class ClientScopeEntity
    {
        public int Id { get; set; }
        public string Scope { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public ClientEntity Client { get; set; } = null!;
    }

    public class ClientGrantTypeEntity
    {
        public int Id { get; set; }
        public string GrantType { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public ClientEntity Client { get; set; } = null!;
    }
}
