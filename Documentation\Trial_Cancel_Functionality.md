# Trial and Cancel Functionality Documentation

## Overview

This document describes the implementation of trial and cancel functionality for lessons/events in the Shining C Music School application. This feature allows administrators to mark lessons as either "Trial" or "Cancelled" with distinct visual indicators and color overrides.

## Features

### Core Functionality
- **Trial Lessons**: Mark lessons as trial sessions with yellow color and star icon
- **Cancelled Lessons**: Mark lessons as cancelled with gray color and X icon
- **Color Override**: Trial and cancelled status override tutor-assigned colors
- **Visual Indicators**: Clear icons and badges in both schedule view and popups
- **Recurring Support**: Works with both single events and recurring series

### User Interface
- Checkboxes in event editor for easy status selection
- Real-time color updates in schedule view
- Status badges in QuickInfo popups
- Icons in event templates for quick identification

## Implementation Details

### Database Schema Changes

#### New Columns Added to Lessons Table
```sql
-- IsTrial column for trial lessons
ALTER TABLE [dbo].[Lessons]
ADD [IsTrial] [bit] NOT NULL DEFAULT 0;

-- IsCancelled column for cancelled lessons  
ALTER TABLE [dbo].[Lessons]
ADD [IsCancelled] [bit] NOT NULL DEFAULT 0;
```

**Column Details:**
- **IsTrial**: Boolean field indicating if lesson is a trial (default: false)
- **IsCancelled**: Boolean field indicating if lesson is cancelled (default: false)
- Both columns are NOT NULL with default value of 0 (false)

### Model Updates

#### Lesson Model (ShiningCMusicCommon/Models/Lesson.cs)
```csharp
public bool IsTrial { get; set; } = false;
public bool IsCancelled { get; set; } = false;
```

#### ScheduleEvent Model (ShiningCMusicCommon/Models/ScheduleEvent.cs)
```csharp
public bool IsTrial { get; set; } = false;
public bool IsCancelled { get; set; } = false;
```

**Conversion Methods Updated:**
- `FromLesson()`: Maps IsTrial and IsCancelled from Lesson to ScheduleEvent
- `ToLesson()`: Maps IsTrial and IsCancelled from ScheduleEvent to Lesson

### API Service Changes

#### LessonService Updates (ShiningCMusicApi/Services/Implementations/LessonService.cs)

**SELECT Queries:**
- Added `l.IsTrial, l.IsCancelled` to all SELECT statements
- Both `GetLessonsAsync()` and `GetLessonByIdAsync()` methods updated

**INSERT Operations:**
```sql
INSERT INTO Lessons (..., IsTrial, IsCancelled, ...)
VALUES (..., @IsTrial, @IsCancelled, ...)
```

**UPDATE Operations:**
```sql
UPDATE Lessons
SET ..., IsTrial = @IsTrial, IsCancelled = @IsCancelled, ...
WHERE LessonId = @Id
```

### User Interface Implementation

#### Event Editor Form (ShiningCMusicApp/Pages/Lessons.razor)

**Trial Lesson Checkbox:**
```html
<div class="form-check">
    <input class="form-check-input" type="checkbox" @bind="scheduleEvent.IsTrial" id="isTrialCheck">
    <label class="form-check-label" for="isTrialCheck">
        <i class="bi bi-star me-1"></i>Trial Lesson
    </label>
    <small class="form-text text-muted d-block">Mark this as a trial lesson (yellow color)</small>
</div>
```

**Cancelled Lesson Checkbox:**
```html
<div class="form-check">
    <input class="form-check-input" type="checkbox" @bind="scheduleEvent.IsCancelled" id="isCancelledCheck">
    <label class="form-check-label" for="isCancelledCheck">
        <i class="bi bi-x-circle me-1"></i>Cancelled
    </label>
    <small class="form-text text-muted d-block">Mark this lesson as cancelled (gray color)</small>
</div>
```

#### Color Override Logic (ShiningCMusicApp/Pages/Lessons.razor.cs)

**Priority Order:**
1. **Cancelled** → Gray (`#6C757D`)
2. **Trial** → Yellow (`#FFC107`)
3. **Tutor Color** → Assigned tutor color
4. **Default** → Gray (`#6C757D`)

```csharp
// Color assignment logic
if (lesson.IsCancelled)
{
    lesson.CategoryColor = "#6C757D"; // Gray for cancelled lessons
}
else if (lesson.IsTrial)
{
    lesson.CategoryColor = "#FFC107"; // Yellow for trial lessons
}
else if (lesson.TutorId > 0)
{
    var tutor = tutors.FirstOrDefault(t => t.TutorId == lesson.TutorId);
    lesson.CategoryColor = tutor?.Color ?? "#6C757D";
}
```

#### Visual Indicators

**Event Template Icons:**
```html
@if (eventData?.IsTrial == true)
{
    <i class="bi bi-star-fill text-warning ms-1" title="Trial Lesson"></i>
}
@if (eventData?.IsCancelled == true)
{
    <i class="bi bi-x-circle-fill text-muted ms-1" title="Cancelled"></i>
}
```

**QuickInfo Popup Badges:**
```html
@if (eventData?.IsTrial == true)
{
    <span class="badge bg-warning text-dark ms-2">
        <i class="bi bi-star-fill me-1"></i>Trial
    </span>
}
@if (eventData?.IsCancelled == true)
{
    <span class="badge bg-secondary ms-2">
        <i class="bi bi-x-circle-fill me-1"></i>Cancelled
    </span>
}
```

## Usage Guide

### For Administrators

#### Creating a Trial Lesson
1. Click on a time slot or edit an existing lesson
2. Fill in the lesson details (subject, tutor, student, etc.)
3. Check the "Trial Lesson" checkbox
4. Save the lesson
5. The lesson will appear in yellow with a star icon

#### Marking a Lesson as Cancelled
1. Edit an existing lesson
2. Check the "Cancelled" checkbox
3. Save the lesson
4. The lesson will appear in gray with an X icon

#### Working with Recurring Events
- **Single Occurrence**: Edit one occurrence to mark only that instance
- **Entire Series**: Edit the series to apply status to all occurrences
- Status can be different for each occurrence in a recurring series

### Visual Reference

| Status | Color | Icon | Badge |
|--------|-------|------|-------|
| Normal | Tutor Color | None | None |
| Trial | Yellow (#FFC107) | ⭐ Star | "Trial" |
| Cancelled | Gray (#6C757D) | ❌ X-Circle | "Cancelled" |

## Technical Notes

### Color Management
- Trial and cancelled lessons maintain their special colors even when tutor colors are changed
- The `OnTutorColorChanged` method respects trial/cancelled status
- Color changes only affect normal lessons, not trial or cancelled ones

### Database Migration
The database migration script is located at:
- `ShiningCMusicApi/SQL/Add_Recurring_Events_Support.sql`
- Includes checks to prevent duplicate column creation
- Safe to run multiple times

### Compatibility
- Works with existing lesson data (new columns default to false)
- Compatible with all existing features (recurring events, tutor colors, etc.)
- No breaking changes to existing functionality

## Testing Checklist

### Basic Functionality
- [ ] Create new trial lesson (appears yellow with star)
- [ ] Create new cancelled lesson (appears gray with X)
- [ ] Edit existing lesson to add trial status
- [ ] Edit existing lesson to add cancelled status
- [ ] Remove trial/cancelled status from lesson

### Recurring Events
- [ ] Mark single occurrence as trial in recurring series
- [ ] Mark single occurrence as cancelled in recurring series
- [ ] Mark entire recurring series as trial
- [ ] Mark entire recurring series as cancelled
- [ ] Mix of statuses within same recurring series

### Visual Verification
- [ ] Trial lessons show yellow color
- [ ] Cancelled lessons show gray color
- [ ] Icons appear in schedule view
- [ ] Badges appear in QuickInfo popups
- [ ] Tutor color changes don't affect trial/cancelled lessons

### Edge Cases
- [ ] Lesson marked as both trial and cancelled (cancelled takes priority)
- [ ] Changing tutor on trial/cancelled lesson (maintains special color)
- [ ] Deleting and recreating lessons with status

## Files Modified

### Database
- `ShiningCMusicApi/SQL/Add_Recurring_Events_Support.sql`

### Models
- `ShiningCMusicCommon/Models/Lesson.cs`
- `ShiningCMusicCommon/Models/ScheduleEvent.cs`

### Services
- `ShiningCMusicApi/Services/Implementations/LessonService.cs`

### UI Components
- `ShiningCMusicApp/Pages/Lessons.razor`
- `ShiningCMusicApp/Pages/Lessons.razor.cs`

### Documentation
- `Documentation/Trial_Cancel_Functionality.md`
- `Documentation/Trial_Cancel_Quick_Reference.md`
- `Documentation/Trial_Cancel_Technical_Summary.md`

## Future Enhancements

### Potential Improvements
- Add trial lesson statistics/reporting
- Implement trial-to-regular conversion workflow
- Add bulk status update functionality
- Create filters for trial/cancelled lessons
- Add email notifications for cancelled lessons
- Implement trial lesson expiration dates

### Configuration Options
- Customizable colors for trial/cancelled lessons
- Configurable icons for different statuses
- Admin-only vs tutor-accessible status changes
