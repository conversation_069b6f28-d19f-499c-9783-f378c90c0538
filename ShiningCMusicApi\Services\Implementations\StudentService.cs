using Dapper;
using System.Data.SqlClient;
using ShiningCMusicCommon.Models;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.Implementations
{
    public class StudentService : IStudentService
    {
        private readonly string _connectionString;

        public StudentService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<IEnumerable<Student>> GetStudentsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Students WHERE IsArchived = 0 ORDER BY StudentName";
            return await connection.QueryAsync<Student>(sql);
        }

        public async Task<Student?> GetStudentAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Students WHERE StudentId = @Id AND IsArchived = 0";
            return await connection.QueryFirstOrDefaultAsync<Student>(sql, new { Id = id });
        }

        public async Task<Student> CreateStudentAsync(Student student)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Students (StudentName, Email, TutorID, SubjectId, LoginName, CreatedUTC, IsArchived)
                VALUES (@StudentName, @Email, @TutorID, @SubjectId, @LoginName, GETUTCDATE(), 0);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                student.StudentName,
                student.Email,
                student.TutorID,
                student.SubjectId,
                student.LoginName
            });

            student.StudentId = newId;
            return student;
        }

        public async Task<bool> UpdateStudentAsync(int id, Student student)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Students
                SET StudentName = @StudentName,
                    Email = @Email,
                    TutorID = @TutorID,
                    SubjectId = @SubjectId,
                    LoginName = @LoginName,
                    UpdatedUTC = GETUTCDATE()
                WHERE StudentId = @Id AND IsArchived = 0";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                student.StudentName,
                student.Email,
                student.TutorID,
                student.SubjectId,
                student.LoginName
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteStudentAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Students
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE StudentId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<int> PermanentlyDeleteArchivedStudentsAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                DELETE FROM Students
                WHERE IsArchived = 1
                AND UpdatedUTC IS NOT NULL
                AND UpdatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var rowsAffected = await connection.ExecuteAsync(sql, new { OlderThanDays = olderThanDays });
            return rowsAffected;
        }

        public async Task<int> GetArchivedStudentsCountAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT COUNT(*)
                FROM Students
                WHERE IsArchived = 1
                AND UpdatedUTC IS NOT NULL
                AND UpdatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var count = await connection.QuerySingleAsync<int>(sql, new { OlderThanDays = olderThanDays });
            return count;
        }
    }
}
